from torch import nn
import torch
import torch.nn.functional as F
from torch.autograd import Variable
import math
from torch.nn import Parameter
from typing import List, Tuple, Union

class ChannelAttention(nn.Module):
    """通道注意力模块
    
    Args:
        in_planes (int): 输入通道数
        ratio (int): 降维比例
    """
    def __init__(self, in_planes: int, ratio: int = 16):
        super(ChannelAttention, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        
        self.fc = nn.Sequential(
            nn.Conv2d(in_planes, in_planes // ratio, 1, bias=False),
            nn.ReLU(),
            nn.Conv2d(in_planes // ratio, in_planes, 1, bias=False)
        )
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        out = avg_out + max_out
        return torch.sigmoid(out)

class SpatialAttention(nn.Module):
    """空间注意力模块
    
    Args:
        kernel_size (int): 卷积核大小
    """
    def __init__(self, kernel_size: int = 7):
        super(SpatialAttention, self).__init__()
        self.conv = nn.Conv2d(2, 1, kernel_size, padding=kernel_size//2, bias=False)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        x = torch.cat([avg_out, max_out], dim=1)
        x = self.conv(x)
        return torch.sigmoid(x)

class Bottleneck(nn.Module):
    """瓶颈层模块，使用深度可分离卷积和CBAM注意力机制来减少计算量并增强特征表达
    
    Args:
        inp (int): 输入通道数
        oup (int): 输出通道数 
        stride (int): 步长，控制特征图尺寸
        expansion (int): 扩展因子，控制中间层通道数的倍数
    """
    def __init__(self, inp: int, oup: int, stride: int, expansion: int):
        super(Bottleneck, self).__init__()
        self.connect = stride == 1 and inp == oup  # 判断是否需要残差连接
        
        self.conv = nn.Sequential(
            # Point-wise卷积，升维
            nn.Conv2d(inp, inp * expansion, 1, 1, 0, bias=False),
            nn.BatchNorm2d(inp * expansion),
            nn.PReLU(inp * expansion),

            # Depth-wise卷积，特征提取
            nn.Conv2d(inp * expansion, inp * expansion, 3, stride, 1, groups=inp * expansion, bias=False),
            nn.BatchNorm2d(inp * expansion),
            nn.PReLU(inp * expansion),

            # Point-wise线性卷积，降维
            nn.Conv2d(inp * expansion, oup, 1, 1, 0, bias=False),
            nn.BatchNorm2d(oup),
        )
        
        # CBAM注意力模块
        self.ca = ChannelAttention(oup)
        self.sa = SpatialAttention()

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        out = self.conv(x)
        # 应用CBAM注意力机制
        out = out * self.ca(out)
        out = out * self.sa(out)
        if self.connect:
            return x + out  # 残差连接
        return out

class BottleneckBaseline(nn.Module):
    """基准瓶颈层模块，不使用CBAM注意力机制
    
    Args:
        inp (int): 输入通道数
        oup (int): 输出通道数 
        stride (int): 步长，控制特征图尺寸
        expansion (int): 扩展因子，控制中间层通道数的倍数
    """
    def __init__(self, inp: int, oup: int, stride: int, expansion: int):
        super(BottleneckBaseline, self).__init__()
        self.connect = stride == 1 and inp == oup  # 判断是否需要残差连接
        
        self.conv = nn.Sequential(
            # Point-wise卷积，升维
            nn.Conv2d(inp, inp * expansion, 1, 1, 0, bias=False),
            nn.BatchNorm2d(inp * expansion),
            nn.PReLU(inp * expansion),

            # Depth-wise卷积，特征提取
            nn.Conv2d(inp * expansion, inp * expansion, 3, stride, 1, groups=inp * expansion, bias=False),
            nn.BatchNorm2d(inp * expansion),
            nn.PReLU(inp * expansion),

            # Point-wise线性卷积，降维
            nn.Conv2d(inp * expansion, oup, 1, 1, 0, bias=False),
            nn.BatchNorm2d(oup),
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        out = self.conv(x)
        if self.connect:
            return x + out  # 残差连接
        return out
  
class ConvBlock(nn.Module):
    """通用卷积块，支持标准卷积和深度可分离卷积
    
    Args:
        inp (int): 输入通道数
        oup (int): 输出通道数
        k (Union[int, Tuple[int, int]]): 卷积核大小
        s (int): 步长
        p (int): 填充大小
        dw (bool): 是否使用深度可分离卷积
        linear (bool): 是否使用线性激活(无激活)
    """
    def __init__(self, inp: int, oup: int, k: Union[int, Tuple[int, int]], 
                 s: int, p: int, dw: bool = False, linear: bool = False):
        super(ConvBlock, self).__init__()
        self.linear = linear
        if dw:  # 深度可分离卷积
            self.conv = nn.Conv2d(inp, oup, k, s, p, groups=inp, bias=False)
        else:  # 标准卷积
            self.conv = nn.Conv2d(inp, oup, k, s, p, bias=False)
        self.bn = nn.BatchNorm2d(oup)
        if not linear:
            self.prelu = nn.PReLU(oup)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.conv(x)
        x = self.bn(x)
        if self.linear:
            return x
        return self.prelu(x)

# MobileFaceNet网络结构配置
Mobilefacenet_bottleneck_setting = [
    # t: 扩展因子, c: 输出通道数, n: 重复次数, s: 初始步长
    [2, 64, 5, 2],   # 降采样
    [4, 128, 1, 2],  # 降采样
    [2, 128, 6, 1],  # 维持尺寸
    [4, 128, 1, 2],  # 降采样
    [2, 128, 2, 1]   # 维持尺寸
]

# MobileNetV2网络结构配置
Mobilenetv2_bottleneck_setting = [
    # t: 扩展因子, c: 输出通道数, n: 重复次数, s: 初始步长
    [1, 16, 1, 1],
    [6, 24, 2, 2],
    [6, 32, 3, 2],
    [6, 64, 4, 2],
    [6, 96, 3, 1],
    [6, 160, 3, 2],
    [6, 320, 1, 1],
]

class MobileFacenet(nn.Module):
    """MobileFaceNet模型主体结构（带CBAM注意力）
    
    Args:
        bottleneck_setting (List): 网络结构配置列表
    """
    def __init__(self, bottleneck_setting: List = Mobilefacenet_bottleneck_setting):
        super(MobileFacenet, self).__init__()

        # 初始卷积层，降采样
        self.conv1 = ConvBlock(3, 64, 3, 2, 1)

        # 深度可分离卷积，特征提取
        self.dw_conv1 = ConvBlock(64, 64, 3, 1, 1, dw=True)

        # Bottleneck模块堆叠
        self.inplanes = 64
        block = Bottleneck
        self.blocks = self._make_layer(block, bottleneck_setting)

        # 特征整合
        self.conv2 = ConvBlock(128, 512, 1, 1, 0)

        # 全局特征提取
        self.linear7 = ConvBlock(512, 512, (7, 6), 1, 0, dw=True, linear=True)

        # 降维
        self.linear1 = ConvBlock(512, 128, 1, 1, 0, linear=True)

        # 权重初始化
        self._initialize_weights()

    def _make_layer(self, block: nn.Module, setting: List) -> nn.Sequential:
        """构建Bottleneck层
        
        Args:
            block: Bottleneck类
            setting: 网络结构配置
        """
        layers = []
        for t, c, n, s in setting:
            for i in range(n):
                if i == 0:
                    layers.append(block(self.inplanes, c, s, t))
                else:
                    layers.append(block(self.inplanes, c, 1, t))
                self.inplanes = c
        return nn.Sequential(*layers)

    def _initialize_weights(self) -> None:
        """初始化模型权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                n = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
                m.weight.data.normal_(0, math.sqrt(2. / n))
            elif isinstance(m, nn.BatchNorm2d):
                m.weight.data.fill_(1)
                m.bias.data.zero_()

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播
        
        Args:
            x: 输入图像张量, shape [B, 3, H, W]
            
        Returns:
            特征向量, shape [B, 128]
        """
        x = self.conv1(x)
        x = self.dw_conv1(x)
        x = self.blocks(x)
        x = self.conv2(x)
        x = self.linear7(x)
        x = self.linear1(x)
        x = x.view(x.size(0), -1)
        return x

class MobileFacenetBaseline(nn.Module):
    """MobileFaceNet基准模型（不带CBAM注意力）
    
    Args:
        bottleneck_setting (List): 网络结构配置列表
    """
    def __init__(self, bottleneck_setting: List = Mobilefacenet_bottleneck_setting):
        super(MobileFacenetBaseline, self).__init__()

        # 初始卷积层，降采样
        self.conv1 = ConvBlock(3, 64, 3, 2, 1)

        # 深度可分离卷积，特征提取
        self.dw_conv1 = ConvBlock(64, 64, 3, 1, 1, dw=True)

        # Bottleneck模块堆叠（不使用CBAM）
        self.inplanes = 64
        block = BottleneckBaseline
        self.blocks = self._make_layer(block, bottleneck_setting)

        # 特征整合
        self.conv2 = ConvBlock(128, 512, 1, 1, 0)

        # 全局特征提取
        self.linear7 = ConvBlock(512, 512, (7, 6), 1, 0, dw=True, linear=True)

        # 降维
        self.linear1 = ConvBlock(512, 128, 1, 1, 0, linear=True)

        # 权重初始化
        self._initialize_weights()

    def _make_layer(self, block: nn.Module, setting: List) -> nn.Sequential:
        """构建Bottleneck层
        
        Args:
            block: Bottleneck类
            setting: 网络结构配置
        """
        layers = []
        for t, c, n, s in setting:
            for i in range(n):
                if i == 0:
                    layers.append(block(self.inplanes, c, s, t))
                else:
                    layers.append(block(self.inplanes, c, 1, t))
                self.inplanes = c
        return nn.Sequential(*layers)

    def _initialize_weights(self) -> None:
        """初始化模型权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                n = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
                m.weight.data.normal_(0, math.sqrt(2. / n))
            elif isinstance(m, nn.BatchNorm2d):
                m.weight.data.fill_(1)
                m.bias.data.zero_()

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播
        
        Args:
            x: 输入图像张量, shape [B, 3, H, W]
            
        Returns:
            特征向量, shape [B, 128]
        """
        x = self.conv1(x)
        x = self.dw_conv1(x)
        x = self.blocks(x)
        x = self.conv2(x)
        x = self.linear7(x)
        x = self.linear1(x)
        x = x.view(x.size(0), -1)
        return x

class ArcMarginProduct(nn.Module):
    """ArcFace损失函数的margin-based cosine layer实现
    
    Args:
        in_features (int): 输入特征维度
        out_features (int): 输出类别数
        s (float): 特征缩放因子
        m (float): 角度margin
        easy_margin (bool): 是否使用easy margin
    """
    def __init__(self, in_features: int = 128, out_features: int = 200, 
                 s: float = 32.0, m: float = 0.50, easy_margin: bool = False):
        super(ArcMarginProduct, self).__init__()
        self.in_features = in_features
        self.out_features = out_features
        self.s = s
        self.m = m
        
        # 初始化权重
        self.weight = Parameter(torch.Tensor(out_features, in_features))
        nn.init.xavier_uniform_(self.weight)

        # 预计算角度参数
        self.easy_margin = easy_margin
        self.cos_m = math.cos(m)
        self.sin_m = math.sin(m)
        self.th = math.cos(math.pi - m)  # 余弦函数阈值
        self.mm = math.sin(math.pi - m) * m  # margin惩罚项

    def forward(self, x: torch.Tensor, label: torch.Tensor) -> torch.Tensor:
        """前向传播
        
        Args:
            x: 输入特征, shape [B, in_features]
            label: 目标标签, shape [B]
            
        Returns:
            输出logits, shape [B, out_features]
        """
        # 计算cosine相似度
        cosine = F.linear(F.normalize(x), F.normalize(self.weight))
        
        # 计算sine值
        sine = torch.sqrt(1.0 - torch.pow(cosine, 2))
        
        # 添加angular margin
        phi = cosine * self.cos_m - sine * self.sin_m
        
        if self.easy_margin:
            phi = torch.where(cosine > 0, phi, cosine)
        else:
            phi = torch.where((cosine - self.th) > 0, phi, cosine - self.mm)

        # 转换为one-hot编码 - 使用x.device而不是硬编码的'cuda'
        one_hot = torch.zeros(cosine.size(), device=x.device)
        one_hot.scatter_(1, label.view(-1, 1).long(), 1)
        
        # 计算最终输出
        output = (one_hot * phi) + ((1.0 - one_hot) * cosine)
        output *= self.s
        
        return output

if __name__ == "__main__":
    # 测试代码
    input = Variable(torch.FloatTensor(2, 3, 112, 96))  # 批大小为2，3通道，112x96分辨率
    net = MobileFacenet()
    print(net)  # 打印网络结构
    x = net(input)
    print(x.shape)  # 打印输出特征维度
