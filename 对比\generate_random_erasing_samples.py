#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成随机擦除遮挡样本脚本
使用ComplexSceneEvaluator类生成随机擦除效果的可视化图像

作者: AI Assistant
创建时间: 2024
"""

import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'  # 避免OpenMP警告
import sys
from pathlib import Path
import argparse

# 添加父目录到路径以便导入模块
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
sys.path.insert(0, str(parent_dir))

from complex_scene_evaluator import ComplexSceneEvaluator

def generate_random_erasing_samples(lfw_dir: str, output_dir: str = None, num_samples: int = 6):
    """
    生成随机擦除遮挡效果的样本图像
    
    参数:
        lfw_dir: LFW数据集目录路径
        output_dir: 输出目录路径，默认为comparison_results/visualizations
        num_samples: 样本数量
    """
    print("🚀 开始生成随机擦除遮挡样本...")
    
    # 设置默认输出目录
    if output_dir is None:
        output_dir = os.path.join(current_dir, "comparison_results", "visualizations")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建场景评估器
    evaluator = ComplexSceneEvaluator(lfw_dir)
    
    # 生成随机擦除场景样本
    output_path = os.path.join(output_dir, "scene_samples_random_erasing.png")
    evaluator.visualize_scene_samples(
        scenario="random_erasing",
        num_samples=num_samples,
        save_path=output_path
    )
    
    print(f"\n✅ 随机擦除遮挡样本已生成: {output_path}")
    return output_path

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='生成随机擦除遮挡样本')
    parser.add_argument('--lfw_dir', type=str, required=True, help='LFW数据集目录路径')
    parser.add_argument('--output_dir', type=str, default=None, help='输出目录路径')
    parser.add_argument('--num_samples', type=int, default=6, help='样本数量')
    
    args = parser.parse_args()
    
    try:
        output_path = generate_random_erasing_samples(
            args.lfw_dir,
            args.output_dir,
            args.num_samples
        )
        print(f"🎉 完成! 图像已保存至: {output_path}")
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main() 