from PIL import Image, ImageFilter, ImageEnhance, ImageDraw, ImageFont
import random
import os

def load_image(path):
    """Load image using PIL"""
    try:
        img = Image.open(path)
        return img.convert('RGB')
    except Exception as e:
        raise ValueError(f"Could not load image from {path}: {e}")

def low_resolution_transform(img, scale_factor=0.3):
    """Apply low resolution transformation"""
    w, h = img.size
    # Downscale
    small = img.resize((int(w * scale_factor), int(h * scale_factor)), Image.LANCZOS)
    # Upscale back
    return small.resize((w, h), Image.NEAREST)

def focus_blur_transform(img, radius=5):
    """Apply focus blur (Gaussian blur)"""
    return img.filter(ImageFilter.GaussianBlur(radius=radius))

def motion_blur_transform(img, size=5):
    """Apply motion blur"""
    # Use a simpler blur approach
    return img.filter(ImageFilter.BLUR)

def exposure_transform(img, gamma=2.5):
    """Apply exposure adjustment (gamma correction)"""
    enhancer = ImageEnhance.Brightness(img)
    # Simulate overexposure by increasing brightness significantly
    return enhancer.enhance(2.0)

def backlight_transform(img, brightness_factor=0.3):
    """Apply backlight effect (darken image)"""
    enhancer = ImageEnhance.Brightness(img)
    return enhancer.enhance(brightness_factor)

def random_occlusion_transform(img, num_patches=3, patch_size_range=(20, 40)):
    """Apply random occlusion patches"""
    result = img.copy()
    draw = ImageDraw.Draw(result)
    w, h = img.size

    for _ in range(num_patches):
        # Random patch size
        patch_w = random.randint(*patch_size_range)
        patch_h = random.randint(*patch_size_range)

        # Random position
        x = random.randint(0, max(0, w - patch_w))
        y = random.randint(0, max(0, h - patch_h))

        # Random dark color
        color = (random.randint(0, 50), random.randint(0, 50), random.randint(0, 50))

        # Draw rectangle
        draw.rectangle([x, y, x + patch_w, y + patch_h], fill=color)

    return result

def create_comparison_plot(img1_path, img2_path, output_path="transformation_comparison.png"):
    """Create comparison plot with transformations in left-right format"""

    # Load images
    img1 = load_image(img1_path)
    img2 = load_image(img2_path)

    # Define transformations
    transformations = [
        ("Low Resolution", low_resolution_transform),
        ("Focus Blur", focus_blur_transform),
        ("Motion Blur", motion_blur_transform),
        ("Exposure", exposure_transform),
        ("Backlight", backlight_transform),
        ("Random Occlusion", random_occlusion_transform)
    ]

    # Get image dimensions
    img_width, img_height = img1.size

    # Add space for text labels
    text_height = 30

    # Create a large canvas for the comparison
    # 2 columns (original and transformed), 6 rows (one for each transformation)
    # Each cell contains 2 images stacked vertically + text space
    canvas_width = img_width * 2  # 2 columns
    canvas_height = (img_height * 2 + text_height) * len(transformations)  # 2 images per row + text * 6 rows

    # Create the main canvas
    canvas = Image.new('RGB', (canvas_width, canvas_height), 'white')
    draw = ImageDraw.Draw(canvas)

    # Try to load a font, fallback to default if not available
    try:
        font = ImageFont.truetype("arial.ttf", 20)
    except:
        font = ImageFont.load_default()

    for i, (transform_name, transform_func) in enumerate(transformations):
        # Calculate positions for this row
        row_y = i * (img_height * 2 + text_height)

        # Add text label at the top of each row
        text_x = canvas_width // 2
        draw.text((text_x, row_y + 5), transform_name, fill='black', font=font, anchor='mt')

        # Adjust image positions to account for text
        img_start_y = row_y + text_height

        # Left column: Original images (Image 1 on top, Image 2 on bottom)
        canvas.paste(img1, (0, img_start_y))
        canvas.paste(img2, (0, img_start_y + img_height))

        # Right column: Transformed images
        try:
            img1_transformed = transform_func(img1.copy())
            img2_transformed = transform_func(img2.copy())
            canvas.paste(img1_transformed, (img_width, img_start_y))
            canvas.paste(img2_transformed, (img_width, img_start_y + img_height))
        except Exception as e:
            print(f"Error applying {transform_name}: {str(e)}")
            # Create error placeholder
            error_img = Image.new('RGB', (img_width, img_height), 'red')
            canvas.paste(error_img, (img_width, img_start_y))
            canvas.paste(error_img, (img_width, img_start_y + img_height))

    # Save the result
    canvas.save(output_path, 'PNG', quality=95)
    print(f"Comparison saved to: {output_path}")

    return canvas

if __name__ == "__main__":
    # Image paths
    img1_path = r"data\CASIA\CASIA-WebFace-112X96\0000268\001.jpg"
    img2_path = r"data\CASIA\CASIA-WebFace-112X96\0002743\002.jpg"
    
    # Check if images exist
    if not os.path.exists(img1_path):
        print(f"Error: Image 1 not found at {img1_path}")
        exit(1)
    
    if not os.path.exists(img2_path):
        print(f"Error: Image 2 not found at {img2_path}")
        exit(1)
    
    # Create comparison
    create_comparison_plot(img1_path, img2_path)
