#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版随机擦除遮挡演示脚本
展示使用torchvision的RandomErasing方法为图像添加随机遮挡的效果

作者: AI Assistant
创建时间: 2024
"""

import numpy as np
import cv2
import matplotlib.pyplot as plt
import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'
import torchvision.transforms as transforms
from PIL import Image
import random

def create_demo_image():
    """创建演示用的人脸图像"""
    # 创建一个简单的人脸模拟图像
    img = np.ones((112, 112, 3), dtype=np.uint8) * 128
    
    # 绘制简单的人脸轮廓
    center = (56, 56)
    
    # 脸部轮廓
    cv2.circle(img, center, 40, (200, 180, 160), -1)
    
    # 眼睛
    cv2.circle(img, (40, 45), 5, (50, 50, 50), -1)
    cv2.circle(img, (72, 45), 5, (50, 50, 50), -1)
    
    # 鼻子
    cv2.line(img, (56, 50), (56, 65), (150, 130, 110), 2)
    
    # 嘴巴
    cv2.ellipse(img, (56, 75), (8, 4), 0, 0, 180, (100, 80, 80), 2)
    
    return img

def apply_random_erasing(image):
    """应用RandomErasing随机擦除效果"""
    # 将numpy图像转换为PIL图像
    pil_img = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    
    # 创建随机擦除转换
    random_erase = transforms.RandomErasing(
        p=1.0,  # 确保应用擦除
        scale=(0.02, 0.33),  # 擦除区域的面积比例范围
        ratio=(0.3, 3.3),  # 擦除区域的宽高比范围
        value=0  # 填充值(黑色)
    )
    
    # 将图像转换为张量
    to_tensor = transforms.ToTensor()
    img_tensor = to_tensor(pil_img)
    
    # 应用随机擦除
    erased_tensor = random_erase(img_tensor)
    
    # 将张量转回PIL图像
    to_pil = transforms.ToPILImage()
    erased_pil = to_pil(erased_tensor)
    
    # 转换回OpenCV格式并返回
    return cv2.cvtColor(np.array(erased_pil), cv2.COLOR_RGB2BGR)

def demo_random_erasing():
    """演示随机擦除效果"""
    print("开始随机擦除演示...")
    
    # 设置随机种子以确保结果可重复
    random.seed(42)
    
    # 创建演示图像
    original_img = create_demo_image()
    
    # 应用随机擦除
    try:
        erased_img = apply_random_erasing(original_img)
        
        # 创建图像显示
        plt.figure(figsize=(10, 5))
        
        # 显示原始图像
        plt.subplot(1, 2, 1)
        plt.imshow(cv2.cvtColor(original_img, cv2.COLOR_BGR2RGB))
        plt.title('原始图像')
        plt.axis('off')
        
        # 显示擦除后的图像
        plt.subplot(1, 2, 2)
        plt.imshow(cv2.cvtColor(erased_img, cv2.COLOR_BGR2RGB))
        plt.title('随机擦除效果')
        plt.axis('off')
        
        plt.tight_layout()
        
        # 保存图像
        output_path = 'simple_random_erasing_demo.png'
        plt.savefig(output_path, dpi=300)
        print(f"演示图像已保存至: {output_path}")
        
        plt.close()
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    demo_random_erasing() 