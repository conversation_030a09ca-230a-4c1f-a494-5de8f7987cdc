import os
import random
import shutil
from collections import defaultdict
import argparse
from tqdm import tqdm
import logging
import numpy as np
from config import CASIA_DATA_DIR, PROCESSED_CASIA_DIR

def setup_logger():
    """设置日志记录器"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('casia_processing.log')
        ]
    )
    return logging.getLogger(__name__)

def process_casia_dataset(root_dir: str, 
                          output_dir: str,
                          min_images_per_identity: int = 30,
                          max_images_per_identity: int = 50,
                          num_identities: int = 350,
                          balance_strategy: str = 'equal',
                          target_total_images: int = 15000,
                          create_files: bool = True):
    """处理CASIA-WebFace数据集
    
    Args:
        root_dir: 原始CASIA数据集根目录
        output_dir: 处理后的数据集输出目录
        min_images_per_identity: 每个身份最少图片数量
        max_images_per_identity: 每个身份最多图片数量
        num_identities: 选择的身份数量
        balance_strategy: 平衡策略，可选'equal'(每个身份相同数量)或'proportional'(按比例分配)
        target_total_images: 目标总图片数量
        create_files: 是否创建实际文件（False则只生成索引文件）
    
    Returns:
        dict: 包含处理结果的字典
    """
    logger = setup_logger()
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 读取原始数据集索引文件
    img_txt_dir = os.path.join(root_dir, 'CASIA-WebFace-112X96.txt')
    if not os.path.exists(img_txt_dir):
        raise FileNotFoundError(f"找不到CASIA数据集索引文件: {img_txt_dir}")
    
    # 读取图像列表
    logger.info(f"读取CASIA数据集索引文件: {img_txt_dir}")
    
    # 按身份分组
    identity_images = defaultdict(list)
    
    with open(img_txt_dir) as f:
        img_label_list = f.read().splitlines()
    
    logger.info(f"CASIA数据集总共有 {len(img_label_list)} 张图片")
    logger.info("按身份分组图片...")
    
    for info in img_label_list:
        image_dir, label_name = info.split(' ')
        identity_images[int(label_name)].append(image_dir)
    
    # 筛选掉图片少于min_images_per_identity的身份
    filtered_identities = {
        identity: images 
        for identity, images in identity_images.items() 
        if len(images) >= min_images_per_identity
    }
    
    logger.info(f"筛选后的身份数量: {len(filtered_identities)}/{len(identity_images)}")
    logger.info(f"要求每个身份至少 {min_images_per_identity} 张图片")
    
    # 排序身份，优先选择图片数量适中的身份
    # 按每个身份的图片数量排序，优先选择图片数量在min_images_per_identity到max_images_per_identity之间的身份
    identity_counts = [(identity, len(filtered_identities[identity])) for identity in filtered_identities.keys()]
    identity_counts.sort(key=lambda x: abs(x[1] - (min_images_per_identity + max_images_per_identity)/2))
    
    # 如果指定了num_identities，则使用指定值，否则自动计算
    if num_identities <= 0:
        # 自动计算需要的身份数量，估计每个身份平均使用min_images_per_identity张图片
        estimated_num_identities = min(
            len(filtered_identities),
            max(1, target_total_images // min_images_per_identity)
        )
        logger.info(f"自动计算的身份数量: {estimated_num_identities}")
        num_identities = estimated_num_identities
    else:
        # 确保不超过可用身份数量
        num_identities = min(num_identities, len(filtered_identities))
    
    # 选择身份
    selected_identities = [identity for identity, _ in identity_counts[:num_identities]]
    selected_identities.sort()  # 排序以保持稳定性
    
    logger.info(f"选择的身份数量: {len(selected_identities)}")
    
    # 统计选择的身份的图片数量分布
    selected_counts = [len(filtered_identities[identity]) for identity in selected_identities]
    logger.info(f"选择的身份图片数量统计: 最小={min(selected_counts)}, 最大={max(selected_counts)}, 平均={np.mean(selected_counts):.1f}, 中位数={np.median(selected_counts)}")
    
    # 限制每个身份的图片数量
    limited_identities = {}
    
    # 根据平衡策略确定每个身份的图片数量
    if balance_strategy == 'equal':
        # 计算每个身份应该使用多少张图片，以达到目标总图片数
        # 确保每个身份至少有min_images_per_identity张图片
        target_count = max(
            min_images_per_identity,
            min(max_images_per_identity, target_total_images // len(selected_identities))
        )
        
        logger.info(f"使用'equal'平衡策略，每个身份使用{target_count}张图片")
        
        for identity in selected_identities:
            images = filtered_identities[identity]
            if len(images) > target_count:
                # 随机选择target_count张图片
                random.seed(identity)  # 为每个身份使用不同的随机种子
                limited_identities[identity] = random.sample(images, target_count)
            else:
                limited_identities[identity] = images
    else:
        # 按比例分配，确保总数量接近目标值
        total_available = sum(len(filtered_identities[identity]) for identity in selected_identities)
        
        logger.info(f"使用'proportional'平衡策略，目标总图片数量: {target_total_images}")
        
        for identity in selected_identities:
            images = filtered_identities[identity]
            # 按比例计算这个身份应该使用的图片数量
            target_count = min(
                max_images_per_identity,  # 不超过最大限制
                max(
                    min_images_per_identity,  # 不少于最小限制
                    int(len(images) * target_total_images / total_available)  # 按比例分配
                )
            )
            
            if len(images) > target_count:
                # 随机选择target_count张图片
                random.seed(identity)  # 为每个身份使用不同的随机种子
                limited_identities[identity] = random.sample(images, target_count)
            else:
                limited_identities[identity] = images
    
    # 计算总图片数量
    total_images = sum(len(imgs) for imgs in limited_identities.values())
    logger.info(f"限制每个身份图片数量后，总图片数量: {total_images} (目标: {target_total_images})")
    
    # 所有身份都用作训练集，不再分割训练集和验证集
    train_identities = set(selected_identities)
    
    # 创建标签映射，从0开始连续标签
    train_label_mapping = {identity: idx for idx, identity in enumerate(sorted(list(train_identities)))}
    
    logger.info(f"训练集身份数量: {len(train_identities)}")
    logger.info(f"训练集图片数量: {total_images}")
    
    # 创建训练集索引文件
    train_file_path = os.path.join(output_dir, 'casia_train.txt')
    
    with open(train_file_path, 'w') as f_train:
        for identity in sorted(train_identities):
            for image_path in limited_identities[identity]:
                f_train.write(f"{image_path} {train_label_mapping[identity]}\n")
    
    logger.info(f"已创建训练集索引文件: {train_file_path}")
    
    # 如果需要创建实际文件
    if create_files:
        train_output_dir = os.path.join(output_dir, 'train')
        os.makedirs(train_output_dir, exist_ok=True)
        
        logger.info("创建训练集文件...")
        
        # 复制训练集图片
        for identity in tqdm(train_identities, desc="复制训练集图片"):
            identity_dir = os.path.join(train_output_dir, str(train_label_mapping[identity]))
            os.makedirs(identity_dir, exist_ok=True)
            
            for image_path in limited_identities[identity]:
                src_path = os.path.join(root_dir, 'CASIA-WebFace-112X96', image_path)
                dst_path = os.path.join(identity_dir, os.path.basename(image_path))
                shutil.copy(src_path, dst_path)
        
        logger.info(f"已创建训练集文件目录: {train_output_dir}")
    
    # 返回处理结果
    results = {
        'total_images': total_images,
        'train_images': total_images,
        'train_identities': len(train_identities),
        'train_file': train_file_path,
        'train_output_dir': os.path.join(output_dir, 'train') if create_files else None
    }
    
    logger.info("数据集处理完成!")
    return results

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='处理CASIA-WebFace数据集')
    parser.add_argument('--root_dir', type=str, default=CASIA_DATA_DIR, help='原始CASIA数据集根目录')
    parser.add_argument('--output_dir', type=str, default=PROCESSED_CASIA_DIR, help='处理后的数据集输出目录')
    parser.add_argument('--min_images', type=int, default=30, help='每个身份最少图片数量')
    parser.add_argument('--max_images', type=int, default=50, help='每个身份最多图片数量')
    parser.add_argument('--num_identities', type=int, default=350, help='选择的身份数量，设置为0则自动计算')
    parser.add_argument('--balance', type=str, default='equal', choices=['equal', 'proportional'], help='数据集平衡策略')
    parser.add_argument('--target_images', type=int, default=15000, help='目标总图片数量')
    parser.add_argument('--only_index', action='store_true', help='仅创建索引文件，不复制实际图片')
    
    args = parser.parse_args()
    
    process_casia_dataset(
        root_dir=args.root_dir,
        output_dir=args.output_dir,
        min_images_per_identity=args.min_images,
        max_images_per_identity=args.max_images,
        num_identities=args.num_identities,
        balance_strategy=args.balance,
        target_total_images=args.target_images,
        create_files=not args.only_index
    ) 