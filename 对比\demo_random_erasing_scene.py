#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
随机擦除遮挡场景演示脚本
展示使用torchvision的RandomErasing方法为图像添加随机遮挡的效果

作者: AI Assistant
创建时间: 2024
"""

import numpy as np
import cv2
import matplotlib.pyplot as plt
import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'  # 避免OpenMP警告
import torchvision.transforms as transforms
from PIL import Image
import random

def create_demo_image():
    """创建演示用的人脸图像"""
    # 创建一个简单的人脸模拟图像
    img = np.ones((112, 112, 3), dtype=np.uint8) * 128
    
    # 绘制简单的人脸轮廓
    center = (56, 56)
    
    # 脸部轮廓
    cv2.circle(img, center, 40, (200, 180, 160), -1)
    
    # 眼睛
    cv2.circle(img, (40, 45), 5, (50, 50, 50), -1)
    cv2.circle(img, (72, 45), 5, (50, 50, 50), -1)
    
    # 鼻子
    cv2.line(img, (56, 50), (56, 65), (150, 130, 110), 2)
    
    # 嘴巴
    cv2.ellipse(img, (56, 75), (8, 4), 0, 0, 180, (100, 80, 80), 2)
    
    return img

def apply_random_erasing(image, p=1.0, scale=(0.02, 0.33), ratio=(0.3, 3.3), value=0):
    """
    使用torchvision的RandomErasing方法为图像添加随机遮挡
    
    参数:
        image: 输入图像 (numpy数组)
        p: 应用擦除的概率
        scale: 擦除区域相对于输入图像的面积比例范围
        ratio: 擦除区域的宽高比范围
        value: 填充值，默认为0（黑色）
    
    返回:
        应用随机擦除后的图像
    """
    # 将numpy图像转换为PIL图像
    pil_img = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    
    # 创建随机擦除转换
    random_erase = transforms.RandomErasing(
        p=p,
        scale=scale,
        ratio=ratio,
        value=value
    )
    
    # 将图像转换为张量
    to_tensor = transforms.ToTensor()
    img_tensor = to_tensor(pil_img)
    
    # 应用随机擦除
    erased_tensor = random_erase(img_tensor)
    
    # 将张量转回PIL图像
    to_pil = transforms.ToPILImage()
    erased_pil = to_pil(erased_tensor)
    
    # 转换回OpenCV格式并返回
    return cv2.cvtColor(np.array(erased_pil), cv2.COLOR_RGB2BGR)

def demo_random_erasing_variations():
    """演示不同参数下的随机擦除效果"""
    print("🎨 开始随机擦除遮挡演示...")
    
    # 创建演示图像
    original_img = create_demo_image()
    
    # 不同的随机擦除参数组合
    erasing_params = [
        {
            'title': '小区域遮挡',
            'p': 1.0, 
            'scale': (0.02, 0.1), 
            'ratio': (0.5, 2.0)
        },
        {
            'title': '中等区域遮挡',
            'p': 1.0, 
            'scale': (0.1, 0.25), 
            'ratio': (0.5, 2.0)
        },
        {
            'title': '大区域遮挡',
            'p': 1.0, 
            'scale': (0.25, 0.4), 
            'ratio': (0.5, 2.0)
        },
        {
            'title': '横向长条遮挡',
            'p': 1.0, 
            'scale': (0.1, 0.2), 
            'ratio': (3.0, 5.0)
        },
        {
            'title': '纵向长条遮挡',
            'p': 1.0, 
            'scale': (0.1, 0.2), 
            'ratio': (0.2, 0.33)
        }
    ]
    
    # 设置随机种子以确保结果可重复
    random.seed(42)
    
    # 创建子图
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle('随机擦除遮挡效果演示 (torchvision.transforms.RandomErasing)', fontsize=16)
    
    # 显示原始图像
    axes[0, 0].imshow(cv2.cvtColor(original_img, cv2.COLOR_BGR2RGB))
    axes[0, 0].set_title('原始图像', fontsize=12)
    axes[0, 0].axis('off')
    
    # 显示不同参数下的随机擦除效果
    for i, params in enumerate(erasing_params):
        row = (i + 1) // 3
        col = (i + 1) % 3
        
        # 应用随机擦除
        erased_img = apply_random_erasing(
            original_img.copy(),
            p=params['p'],
            scale=params['scale'],
            ratio=params['ratio']
        )
        
        # 显示图像
        axes[row, col].imshow(cv2.cvtColor(erased_img, cv2.COLOR_BGR2RGB))
        axes[row, col].set_title(params['title'], fontsize=12)
        axes[row, col].axis('off')
        
        print(f"   ✅ {params['title']}变换完成")
    
    plt.tight_layout()
    
    # 保存图像
    output_path = 'random_erasing_variations.png'
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"\n📁 演示图像已保存至: {output_path}")
    
    plt.close()

def demo_multiple_erasings():
    """演示同一图像应用多个随机擦除的效果"""
    print("\n🎲 演示多重随机擦除效果...")
    
    # 创建演示图像
    original_img = create_demo_image()
    
    # 设置随机种子
    random.seed(42)
    
    # 创建子图
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle('多重随机擦除效果演示', fontsize=16)
    
    # 显示原始图像
    axes[0, 0].imshow(cv2.cvtColor(original_img, cv2.COLOR_BGR2RGB))
    axes[0, 0].set_title('原始图像', fontsize=12)
    axes[0, 0].axis('off')
    
    # 应用不同数量的随机擦除
    for i in range(5):
        row = (i + 1) // 3
        col = (i + 1) % 3
        
        # 复制原始图像
        img_copy = original_img.copy()
        
        # 应用i+1次随机擦除
        for j in range(i+1):
            img_copy = apply_random_erasing(
                img_copy,
                p=1.0,
                scale=(0.05, 0.15),
                ratio=(0.5, 2.0)
            )
        
        # 显示图像
        axes[row, col].imshow(cv2.cvtColor(img_copy, cv2.COLOR_BGR2RGB))
        axes[row, col].set_title(f'{i+1}次随机擦除', fontsize=12)
        axes[row, col].axis('off')
    
    plt.tight_layout()
    
    # 保存图像
    output_path = 'multiple_random_erasings.png'
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"📁 多重擦除演示已保存至: {output_path}")
    
    plt.close()

def main():
    """主函数"""
    print("🚀 随机擦除遮挡效果演示")
    print("="*50)
    
    try:
        # 演示不同参数下的随机擦除效果
        demo_random_erasing_variations()
        
        # 演示多重随机擦除效果
        demo_multiple_erasings()
        
        print("\n🎉 演示完成!")
        print("📋 生成的文件:")
        print("   • random_erasing_variations.png - 不同参数的随机擦除效果")
        print("   • multiple_random_erasings.png - 多重随机擦除效果")
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main() 