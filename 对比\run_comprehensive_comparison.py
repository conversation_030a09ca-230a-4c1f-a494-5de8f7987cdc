#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人脸识别模型综合对比启动脚本
整合所有对比评估功能的统一入口


"""

import os
import sys
import argparse
import time
from pathlib import Path
from typing import List

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir.parent))
sys.path.insert(0, str(current_dir))

try:
    from comprehensive_model_comparison import ModelComparator
    from complex_scene_evaluator import ComplexSceneEvaluator
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在对比目录中运行此脚本")
    sys.exit(1)

def print_banner():
    """打印欢迎横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    人脸识别模型综合对比评估系统                              ║
║                                                                              ║
║  功能特性:                                                                   ║
║  • 基准模型vs轻量级模型vs主流模型全面对比                                    ║
║  • 准确率、实时性、鲁棒性多维度评估                                          ║ 
║  • 影视复杂场景(低光照/高光/模糊/遮挡/极端姿态/低分辨率)专项测试              ║
║  • TAR@FAR、EER等专业指标计算                                               ║
║  • 自动生成可视化报告和详细分析                                              ║
║                                                                              ║
║  支持模型:                                                                   ║
║  📌 MobileFaceNet+CBAM (基准模型)                                            ║
║  📱 MobileFaceNet (轻量级)                                                   ║
║  🎯 ArcFace-ResNet50 (主流)                                                  ║
║  🧠 FaceNet (主流)                                                          ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """检查依赖库"""
    print("🔍 检查依赖库...")
    
    # 包名映射：pip包名 -> 导入名
    package_mapping = {
        'torch': 'torch',
        'torchvision': 'torchvision', 
        'numpy': 'numpy',
        'opencv-python': 'cv2',
        'matplotlib': 'matplotlib',
        'seaborn': 'seaborn',
        'pandas': 'pandas',
        'tqdm': 'tqdm',
        'prettytable': 'prettytable',
        'scikit-learn': 'sklearn'
    }
    
    missing_packages = []
    
    for pip_name, import_name in package_mapping.items():
        try:
            __import__(import_name)
            print(f"   ✅ {pip_name}")
        except ImportError:
            missing_packages.append(pip_name)
            print(f"   ❌ {pip_name}")
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖库: {', '.join(missing_packages)}")
        print("请使用以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖库检查通过!")
    return True

def check_dataset(lfw_dir: str) -> bool:
    """检查数据集"""
    print(f"🔍 检查LFW数据集: {lfw_dir}")
    
    if not os.path.exists(lfw_dir):
        print(f"❌ 数据集目录不存在: {lfw_dir}")
        return False
    
    pairs_file = os.path.join(lfw_dir, 'pairs.txt')
    if not os.path.exists(pairs_file):
        print(f"❌ 缺少pairs.txt文件: {pairs_file}")
        return False
    
    # 检查是否有图像文件
    image_count = 0
    for root, dirs, files in os.walk(lfw_dir):
        image_count += len([f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
    
    if image_count == 0:
        print("❌ 数据集目录中没有找到图像文件")
        return False
    
    print(f"✅ 数据集检查通过! 找到 {image_count} 张图像")
    return True

def setup_output_directory(output_dir: str) -> Path:
    """设置输出目录"""
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True, parents=True)
    
    # 创建子目录
    subdirs = ['reports', 'visualizations', 'data', 'models']
    for subdir in subdirs:
        (output_path / subdir).mkdir(exist_ok=True)
    
    print(f"📁 输出目录已准备: {output_path.absolute()}")
    return output_path

def run_basic_comparison(comparator: ModelComparator, checkpoint_paths: dict, 
                        skip_models: List[str] = [], quick_test: bool = False) -> None:
    """运行基础模型对比"""
    print("\n" + "="*80)
    print("🔄 开始基础模型对比评估...")
    print("="*80)
    
    start_time = time.time()
    
    # 设置要跳过的模型
    for model in skip_models:
        if model in comparator.model_configs:
            print(f"⚠️ 根据配置跳过模型: {model}")
            comparator.model_configs.pop(model)
    
    # 执行对比
    comparator.compare_all_models(checkpoint_paths, quick_test=quick_test)
    
    # 生成报告
    comparator.generate_report()
    
    elapsed = time.time() - start_time
    print(f"\n✅ 基础对比评估完成! 耗时: {elapsed:.2f}秒")

def run_robustness_evaluation(evaluator: ComplexSceneEvaluator, 
                            comparator: ModelComparator) -> None:
    """运行鲁棒性评估"""
    print("\n" + "="*80)
    print("🔄 开始复杂场景鲁棒性评估...")
    print("="*80)
    
    if not comparator.results:
        print("⚠️  跳过鲁棒性评估 - 没有可用的模型结果")
        return
    
    # 为每个成功加载的模型运行鲁棒性测试
    robustness_results = {}
    
    for model_name in comparator.results.keys():
        print(f"\n📊 评估模型 {model_name} 的鲁棒性...")
        
        try:
            # 重新加载模型
            model = comparator.load_model(model_name)
            if model is not None:
                results = evaluator.evaluate_model_robustness(model)
                robustness_results[model_name] = results
            else:
                print(f"⚠️  跳过模型 {model_name} - 加载失败")
        except Exception as e:
            print(f"❌ 评估模型 {model_name} 鲁棒性时出错: {e}")
    
    if robustness_results:
        # 生成鲁棒性报告
        report_path = comparator.output_dir / 'robustness_detailed_report.md'
        evaluator.generate_robustness_report(robustness_results, str(report_path))
        print(f"✅ 鲁棒性评估完成! 报告已保存")
    else:
        print("⚠️  没有成功完成的鲁棒性评估")

def visualize_scene_transformations(evaluator: ComplexSceneEvaluator, 
                                  output_dir: Path) -> None:
    """可视化场景变换"""
    print("\n" + "="*80)
    print("🔄 生成复杂场景变换可视化...")
    print("="*80)
    
    viz_dir = output_dir / 'visualizations'
    
    for scenario in evaluator.scenarios.keys():
        print(f"🎨 生成 {evaluator.scenarios[scenario]['name']} 变换样本...")
        try:
            save_path = viz_dir / f'scene_samples_{scenario}.png'
            evaluator.visualize_scene_samples(scenario, num_samples=6, save_path=str(save_path))
        except Exception as e:
            print(f"❌ 生成场景 {scenario} 可视化时出错: {e}")
    
    print("✅ 场景变换可视化完成!")

def print_summary(output_dir: Path) -> None:
    """打印总结信息"""
    print("\n" + "="*80)
    print("📊 评估完成总结")
    print("="*80)
    
    print(f"📁 所有结果已保存至: {output_dir.absolute()}")
    print("\n📋 生成的文件:")
    
    # 列出关键文件
    key_files = [
        ('comparison_results.csv', '📈 模型对比结果表格'),
        ('detailed_results.json', '📄 详细结果数据'),
        ('summary_report.md', '📝 总结报告'),
        ('robustness_detailed_report.md', '🛡️  鲁棒性评估报告'),
        ('accuracy_comparison.png', '📊 准确率对比图'),
        ('performance_comparison.png', '⚡ 性能对比图'),
        ('robustness_heatmap.png', '🔥 鲁棒性热图'),
        ('roc_curves.png', '📈 ROC曲线对比'),
    ]
    
    for filename, description in key_files:
        filepath = output_dir / filename
        if filepath.exists():
            print(f"   ✅ {description}: {filename}")
        else:
            print(f"   ⚠️  {description}: {filename} (未生成)")
    
    print(f"\n🎯 下一步:")
    print(f"   1. 查看总结报告: {output_dir / 'summary_report.md'}")
    print(f"   2. 分析详细数据: {output_dir / 'comparison_results.csv'}")
    print(f"   3. 检查可视化图表: {output_dir / 'visualizations'}/")

def main():
    """主函数"""
    print_banner()
    
    parser = argparse.ArgumentParser(
        description='人脸识别模型综合对比评估系统',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 基础对比(只需LFW数据集)
  python run_comprehensive_comparison.py --lfw_dir ./lfw
  
  # 完整对比(包含自定义模型)
  python run_comprehensive_comparison.py \\
    --lfw_dir ./lfw \\
    --output_dir ./results \\
    --mobilefacenet_cbam_ckpt ./checkpoints/mobilefacenet_cbam.pth \\
    --mobilefacenet_ckpt ./checkpoints/mobilefacenet.pth
  
  # 仅可视化场景变换
  python run_comprehensive_comparison.py \\
    --lfw_dir ./lfw \\
    --visualize_only
  
  # 仅可视化随机擦除遮挡场景
  python run_comprehensive_comparison.py \\
    --lfw_dir ./lfw \\
    --random_erasing_only
        """
    )
    
    # 基础参数
    parser.add_argument('--lfw_dir', type=str, required=True,
                       help='LFW数据集路径')
    parser.add_argument('--output_dir', type=str, default='./comparison_results',
                       help='结果输出目录 (默认: ./comparison_results)')
    
    # 模型检查点
    parser.add_argument('--checkpoint_paths', type=str, nargs='+', 
                       help='模型检查点路径列表，格式: model_name:path')
    parser.add_argument('--mobilefacenet_checkpoint', type=str,
                       help='MobileFaceNet+CBAM模型检查点路径')
    parser.add_argument('--skip_models', type=str, nargs='+', default=[],
                       help='跳过的模型列表')
    
    # 控制选项
    parser.add_argument('--skip_basic', action='store_true',
                       help='跳过基础模型对比')
    parser.add_argument('--skip_robustness', action='store_true',
                       help='跳过鲁棒性评估')
    parser.add_argument('--skip_visualization', action='store_true',
                       help='跳过场景变换可视化')
    parser.add_argument('--visualize_only', action='store_true',
                       help='仅运行场景变换可视化')
    parser.add_argument('--random_erasing_only', action='store_true',
                       help='仅可视化随机擦除遮挡场景')
    
    parser.add_argument('--model', type=str, nargs='+', default=[],
                       help='仅包含指定模型 (如: MobileFaceNet+CBAM FaceNet)')
    parser.add_argument('--cbam_checkpoint', type=str, default=None,
                       help='MobileFaceNet+CBAM模型检查点路径 (默认: ../model/best/068.ckpt)')
    parser.add_argument('--quick_test', action='store_true',
                       help='仅对少量数据进行快速测试')
    parser.add_argument('--debug', action='store_true',
                       help='启用调试模式，输出更多信息')
    
    args = parser.parse_args()
    
    # 1. 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 2. 检查数据集
    if not check_dataset(args.lfw_dir):
        sys.exit(1)
    
    # 3. 设置输出目录
    output_dir = setup_output_directory(args.output_dir)
    
    # 4. 初始化评估器
    print("\n🔧 初始化评估器...")
    comparator = ModelComparator(args.lfw_dir, str(output_dir))
    evaluator = ComplexSceneEvaluator(args.lfw_dir)
    
    # 5. 准备检查点路径
    checkpoint_dict = {}
    if args.checkpoint_paths:
        for cp in args.checkpoint_paths:
            if ':' in cp:
                model_name, path = cp.split(':', 1)
                checkpoint_dict[model_name] = path
    
    if args.mobilefacenet_checkpoint:
        checkpoint_dict['MobileFaceNet+CBAM'] = args.mobilefacenet_checkpoint
    
    if args.cbam_checkpoint:
        checkpoint_dict['MobileFaceNet+CBAM'] = args.cbam_checkpoint
    
    # 添加默认的checkpoint路径
    if 'MobileFaceNet+CBAM' not in checkpoint_dict:
        # 尝试多个可能的路径
        possible_paths = [
            os.path.join(current_dir.parent, "model", "best", "068.ckpt"),  # 相对于父目录
            os.path.join(os.path.dirname(current_dir.parent), "model", "best", "068.ckpt"),  # 相对于祖父目录
            "../model/best/068.ckpt",  # 相对于当前脚本
            "../../model/best/068.ckpt",  # 相对于当前脚本(再上一级)
            "/model/best/068.ckpt"  # 绝对路径
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                print(f"✅ 找到MobileFaceNet+CBAM检查点: {path}")
                checkpoint_dict['MobileFaceNet+CBAM'] = path
                break
        else:
            print("⚠️ 未找到MobileFaceNet+CBAM检查点文件，将尝试使用训练好的模型")
    
    # 处理仅包含特定模型
    if args.model:
        models_to_include = set(args.model)
        print(f"仅包含以下模型: {', '.join(models_to_include)}")
        args.skip_models = [m for m in ['MobileFaceNet+CBAM', 'MobileFaceNet', 'ArcFace-R50', 'FaceNet'] 
                           if m not in models_to_include]
    
    # 调试模式
    if args.debug:
        print("启用调试模式")
        print(f"检查点路径: {checkpoint_dict}")
        print(f"跳过模型: {args.skip_models}")
    
    try:
        if args.visualize_only:
            # 仅运行可视化
            visualize_scene_transformations(evaluator, output_dir)
        elif args.random_erasing_only:
            # 仅运行随机擦除遮挡场景可视化
            evaluator.visualize_random_erasing_samples()
        else:
            # 运行完整评估流程
            if not args.skip_basic:
                run_basic_comparison(comparator, checkpoint_dict, args.skip_models, args.quick_test)
            
            if not args.skip_robustness:
                run_robustness_evaluation(evaluator, comparator)
            
            if not args.skip_visualization:
                visualize_scene_transformations(evaluator, output_dir)
        
        # 打印总结
        print_summary(output_dir)
        
        print(f"\n🎉 评估完成! 总耗时: {time.time() - time.time():.2f}秒")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断评估过程")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 评估过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main() 