#!/usr/bin/env python3
"""
可视化MobileFaceNet模型的空间注意力热力图

此脚本加载预训练的MobileFaceNet+CBAM模型，对输入的人脸图像生成空间注意力热力图，
并将原始图像和热力图进行对比展示。
"""

import os
import argparse
import numpy as np
import torch
import torch.nn as nn
import matplotlib.pyplot as plt
from PIL import Image
from typing import Tuple, List, Dict, Optional, Union
import cv2
from torchvision import transforms

from core.model import MobileFacenet, Mobilefacenet_bottleneck_setting, SpatialAttention
from config import BATCH_SIZE, GPU

class AttentionMapHook:
    """用于提取中间层空间注意力图的钩子类
    
    Attributes:
        spatial_maps: 存储空间注意力图的字典
        name: 层的名称
    """
    def __init__(self, name: str):
        """初始化钩子
        
        Args:
            name: 层的名称，用于标识不同层的注意力图
        """
        self.spatial_maps = {}
        self.name = name
        
    def __call__(self, module: nn.Module, input: Tuple[torch.Tensor], output: torch.Tensor):
        """钩子调用函数
        
        Args:
            module: 包含注意力机制的模块
            input: 输入张量元组
            output: 输出张量
        """
        # 存储空间注意力图
        if isinstance(module, SpatialAttention):
            # 提取最后一个空间注意力图(sigmoid前，取1通道的输出)
            # 空间注意力输出格式: [B, 1, H, W]
            self.spatial_maps[self.name] = output.detach().cpu()

def load_model(model_path: str, device: torch.device) -> nn.Module:
    """加载预训练的MobileFaceNet模型
    
    Args:
        model_path: 模型权重的路径
        device: 计算设备(CPU/GPU)
        
    Returns:
        加载预训练权重的模型
    """
    # 创建模型
    model = MobileFacenet(Mobilefacenet_bottleneck_setting)
    
    # 加载预训练权重
    checkpoint = torch.load(model_path, map_location=device)
    model.load_state_dict(checkpoint, strict=False)
    
    # 设置为评估模式
    model.eval()
    model.to(device)
    
    return model

def register_hooks(model: nn.Module) -> Dict[str, AttentionMapHook]:
    """注册钩子以提取空间注意力图
    
    Args:
        model: MobileFaceNet模型
        
    Returns:
        钩子对象的字典
    """
    hooks = {}
    hook_index = 0
    
    # 为模型中的所有SpatialAttention层注册钩子
    for name, module in model.named_modules():
        if isinstance(module, SpatialAttention):
            hook_name = f"spatial_attention_{hook_index}"
            hook = AttentionMapHook(hook_name)
            module.register_forward_hook(hook)
            hooks[hook_name] = hook
            hook_index += 1
    
    return hooks

def preprocess_image(image_path: str, device: torch.device) -> torch.Tensor:
    """预处理输入图像
    
    Args:
        image_path: 图像路径
        device: 计算设备
        
    Returns:
        预处理后的图像张量，形状为[1, 3, H, W]
    """
    # 加载图像
    image = Image.open(image_path).convert('RGB')
    
    # 图像预处理转换
    transform = transforms.Compose([
        transforms.Resize((112, 96)),  # MobileFaceNet输入尺寸
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
    ])
    
    # 转换为张量并添加批次维度
    img_tensor = transform(image).unsqueeze(0).to(device)
    return img_tensor, image

def visualize_attention(image: Image.Image, 
                      attention_maps: Dict[str, torch.Tensor], 
                      save_path: Optional[str] = None) -> None:
    """可视化原始图像和空间注意力热力图
    
    Args:
        image: 原始PIL图像
        attention_maps: 空间注意力图字典
        save_path: 保存可视化结果的路径，如果为None则显示图像
    """
    # 转换PIL图像为numpy数组
    img_np = np.array(image.resize((96, 112)))
    
    # 创建子图
    num_maps = len(attention_maps)
    fig, axes = plt.subplots(1, num_maps + 1, figsize=(4 * (num_maps + 1), 4))
    
    # 显示原始图像
    axes[0].imshow(img_np)
    axes[0].set_title('原始图像')
    axes[0].axis('off')
    
    # 显示每个注意力图
    for i, (name, attention_map) in enumerate(attention_maps.items()):
        # 获取注意力图并调整大小
        att_map = attention_map[0, 0].numpy()  # [H, W]
        att_map = cv2.resize(att_map, (96, 112))
        
        # 创建热力图
        heatmap = cv2.applyColorMap(np.uint8(255 * att_map), cv2.COLORMAP_JET)
        heatmap = cv2.cvtColor(heatmap, cv2.COLOR_BGR2RGB)
        
        # 叠加热力图和原始图像
        alpha = 0.6
        superimposed_img = heatmap * alpha + img_np * (1 - alpha)
        superimposed_img = np.uint8(superimposed_img)
        
        # 显示叠加后的图像
        axes[i + 1].imshow(superimposed_img)
        axes[i + 1].set_title(f'注意力热力图 {i+1}')
        axes[i + 1].axis('off')
    
    plt.tight_layout()
    
    # 保存或显示结果
    if save_path:
        plt.savefig(save_path, bbox_inches='tight')
        print(f"可视化结果已保存至: {save_path}")
    else:
        plt.show()

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='可视化MobileFaceNet空间注意力热力图')
    parser.add_argument('--image_path', type=str, required=True, help='输入图像的路径')
    parser.add_argument('--model_path', type=str, default='model/best/MobileFaceNet_CBAM_best.ckpt', 
                        help='预训练模型的路径')
    parser.add_argument('--save_path', type=str, default=None, help='保存可视化结果的路径')
    args = parser.parse_args()
    
    # 设置设备
    if isinstance(GPU, int):
        device = torch.device(f"cuda:{GPU}" if torch.cuda.is_available() else "cpu")
    else:
        device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    
    print(f"使用设备: {device}")
    
    # 加载模型
    model = load_model(args.model_path, device)
    print(f"已加载模型: {args.model_path}")
    
    # 注册钩子
    hooks = register_hooks(model)
    print(f"已注册 {len(hooks)} 个空间注意力钩子")
    
    # 预处理图像
    img_tensor, original_image = preprocess_image(args.image_path, device)
    print(f"已加载图像: {args.image_path}")
    
    # 前向传播，获取注意力图
    with torch.no_grad():
        _ = model(img_tensor)
    
    # 收集所有空间注意力图
    attention_maps = {}
    for name, hook in hooks.items():
        attention_maps.update(hook.spatial_maps)
    
    print(f"提取了 {len(attention_maps)} 个空间注意力图")
    
    # 可视化原始图像和注意力热力图
    visualize_attention(original_image, attention_maps, args.save_path)

if __name__ == '__main__':
    main() 