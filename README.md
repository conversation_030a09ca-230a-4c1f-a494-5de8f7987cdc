# MobileFaceNet PyTorch 实现

本项目是MobileFaceNet的PyTorch实现，用于人脸识别任务。

## 数据集处理

本项目使用精简版的**CASIA-WebFace**数据集进行训练，具有以下特点：

- 过滤掉图片少于5张的身份
- 随机选择500个身份（可配置）
- 每个身份最多使用30张图片（可配置）
- 将80%的选定身份用于训练，20%用于验证
- 总图片数量控制在约2万张左右

## 项目结构

```
├── core/                   # 核心模型架构
├── dataloader/             # 数据加载器
│   ├── CASIA_Face_loader.py # CASIA数据集加载器
│   └── LFW_loader.py        # LFW评估数据集加载器
├── model/                  # 保存训练模型
├── data/                   # 数据集目录
│   ├── CASIA/              # 原始CASIA数据集
│   ├── processed_casia/    # 处理后的CASIA数据集
│   └── lfw/                # LFW评估数据集
├── config.py               # 配置文件
├── train.py                # 训练脚本
└── process_casia_dataset.py # 数据集处理脚本
```

## 使用方法

### 1. 准备数据集

下载CASIA-WebFace数据集，并将其放置在config.py中指定的路径下。该目录应该包含：
- `CASIA-WebFace-112X96/`文件夹（包含所有图片）
- `CASIA-WebFace-112X96.txt`索引文件

下载LFW数据集，并将其放置在config.py中指定的路径下。

### 2. 处理数据集

运行数据处理脚本，控制数据集大小：

```bash
python process_casia_dataset.py --root_dir [CASIA数据集路径] --output_dir [处理后的数据集输出路径] --min_images 5 --max_images 30 --num_identities 500
```

例如：
```bash
python process_casia_dataset.py --root_dir C:/Users/<USER>/Downloads/MobileFaceNet_Pytorch-master/data/CASIA --output_dir C:/Users/<USER>/Downloads/MobileFaceNet_Pytorch-master/data/processed_casia --min_images 5 --max_images 30 --num_identities 500
```

参数说明：
- `--root_dir`：原始CASIA数据集目录
- `--output_dir`：处理后的数据集输出目录
- `--min_images`：每个身份最少图片数量（默认5）
- `--max_images`：每个身份最多图片数量（默认30）
- `--num_identities`：选择的身份数量（默认500）
- `--train_ratio`：训练集比例（默认0.8）
- `--only_index`：仅创建索引文件，不复制实际图片（可选）

### 3. 修改配置

如果需要，在`config.py`中调整训练参数：

```python
# 修改批量大小、总轮数等
BATCH_SIZE = 64
TOTAL_EPOCH = 50
```

### 4. 开始训练

运行训练脚本：

```bash
python train.py
```

训练过程中，模型将在以下数据集上进行评估：
1. 验证集（来自处理后的CASIA）
2. LFW（用于面部验证）

最佳模型将保存在`model/`目录中。

## 注意事项

1. 使用Windows系统时，请务必确保`train.py`中的`num_workers=0`以避免多进程问题
2. 如果遇到CUDA内存不足问题，请减小`BATCH_SIZE`
3. 可以尝试调整`--max_images`和`--num_identities`来控制数据集总大小在2万张左右 