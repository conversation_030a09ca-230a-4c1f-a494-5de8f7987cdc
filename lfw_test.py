#!/usr/bin/env python3
"""
在LFW数据集上测试人脸识别模型

这是一个独立的测试脚本，用于评估已训练模型在LFW数据集上的性能。
支持三种模型：
1. MobileFaceNet+CBAM
2. MobileFaceNet基准版
3. ArcFace-ResNet50
"""

import os
import numpy as np
import scipy.io
import torch
import torch.nn.functional as F
from torch.cuda.amp import autocast
from torch.utils.data import DataLoader
from tqdm import tqdm
from torchvision import transforms
import logging
import argparse
import time
from datetime import datetime
from PIL import Image
import cv2

# 从项目中导入必要模块
from config import LFW_DATA_DIR
from dataloader.LFW_loader import LFW

def setup_logger(save_dir, filename="lfw_test_results.txt"):
    """设置日志为文本格式"""
    # 确保日志目录存在
    os.makedirs(save_dir, exist_ok=True)
    
    # 创建日志文件名
    log_file = os.path.join(save_dir, filename)
    
    # 配置日志
    logger = logging.getLogger("lfw_test")
    logger.setLevel(logging.INFO)
    
    # 清除之前的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 文件处理器
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.INFO)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 设置格式
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger, log_file

def parseList(root=LFW_DATA_DIR):
    """解析LFW数据集列表"""
    with open(os.path.join(root, 'pairs.txt')) as f:
        pairs = f.read().splitlines()[1:]
    
    nl, nr, folds, flags = [], [], [], []
    
    for i, p in enumerate(pairs):
        p = p.split('\t')
        if len(p) == 3:
            nl.append(os.path.join(root, 'lfw-112X96', p[0], p[0] + '_' + '{:04}.jpg'.format(int(p[1]))))
            nr.append(os.path.join(root, 'lfw-112X96', p[0], p[0] + '_' + '{:04}.jpg'.format(int(p[2]))))
            flags.append(1)
        elif len(p) == 4:
            nl.append(os.path.join(root, 'lfw-112X96', p[0], p[0] + '_' + '{:04}.jpg'.format(int(p[1]))))
            nr.append(os.path.join(root, 'lfw-112X96', p[2], p[2] + '_' + '{:04}.jpg'.format(int(p[3]))))
            flags.append(-1)
        
        folds.append(i // 600)
    
    # 确保使用整数类型的numpy数组
    folds = np.asarray(folds, dtype=np.int32)
    flags = np.asarray(flags, dtype=np.int32)
    
    return nl, nr, folds, flags

def evaluation_10_fold(result_dict, logger=None):
    """10折交叉验证评估"""
    features_l = result_dict['fl']
    features_r = result_dict['fr']
    folds = result_dict['fold']
    flags = result_dict['flag']
    
    # 确保使用整数类型
    folds = folds.astype(np.int32)
    flags = flags.astype(np.int32)
    
    accs = []
    thresholds = []
    
    for i in range(10):
        # 当前折的测试索引
        test_indices = np.where(folds == i)[0]
        
        # 训练索引（其他9折）
        train_indices = np.where(folds != i)[0]
        
        # 拆分训练集和测试集
        features_l_train = features_l[train_indices]
        features_r_train = features_r[train_indices]
        flags_train = flags[train_indices]
        
        features_l_test = features_l[test_indices]
        features_r_test = features_r[test_indices]
        flags_test = flags[test_indices]
        
        # 计算余弦相似度
        dists_train = np.sum(features_l_train * features_r_train, axis=1)
        dists_test = np.sum(features_l_test * features_r_test, axis=1)
        
        # 寻找最佳阈值
        best_acc = 0
        best_threshold = 0
        
        for threshold in np.arange(-1.0, 1.0, 0.01):
            pred_train = dists_train >= threshold
            true_train = flags_train == 1
            
            acc = np.mean(pred_train == true_train)
            if acc > best_acc:
                best_acc = acc
                best_threshold = threshold
        
        # 在测试集上评估
        pred_test = dists_test >= best_threshold
        true_test = flags_test == 1
        
        acc = np.mean(pred_test == true_test)
        accs.append(acc)
        thresholds.append(best_threshold)
        
        if logger:
            logger.info(f"折 {i+1}/10: 准确率 = {acc*100:.2f}%, 最佳阈值 = {best_threshold:.4f}")
    
    # 计算10个折的平均准确率和标准差
    mean_acc = np.mean(accs)
    std_acc = np.std(accs)
    if logger:
        logger.info(f"10折平均准确率: {mean_acc*100:.4f}% ± {std_acc*100:.4f}%")
        logger.info(f"平均阈值: {np.mean(thresholds):.4f} ± {np.std(thresholds):.4f}")
        
    return accs

def prepare_lfw_dataset():
    """准备LFW数据集"""
    nl, nr, folds, flags = parseList(root=LFW_DATA_DIR)
    testdataset = LFW(nl, nr)
    testloader = DataLoader(
        testdataset, 
        batch_size=32, 
        shuffle=False, 
        num_workers=4, 
        pin_memory=True, 
        drop_last=False
    )
    return testloader, folds, flags

def test_on_lfw(model, testloader, folds, flags, device, model_type='mobilefacenet', logger=None):
    """在LFW数据集上测试模型
    
    Args:
        model: 待评估的模型
        testloader: LFW数据加载器
        folds: 折叠信息(numpy array)
        flags: 标识信息(numpy array)
        device: 设备
        model_type: 模型类型，'mobilefacenet', 'mobilefacenet_baseline'或'arcface'
        logger: 日志记录器
        
    Returns:
        float: LFW平均准确率(%)
    """
    model.eval()
    featureLs, featureRs = None, None
    
    if logger:
        logger.info(f"开始在LFW数据集上测试模型({model_type})...")
    
    start_time = time.time()
    
    with torch.no_grad():
        for data in tqdm(testloader, desc='LFW测试中'):
            data = [d.to(device, non_blocking=True) for d in data]
            
            with autocast():
                # 不同模型的特征提取方式不同
                if model_type == 'arcface':
                    # ArcFace模型需要特殊处理
                    try:
                        # 首先尝试使用return_features参数
                        res = [model(d, return_features=True).cpu().numpy() for d in data]
                    except:
                        # 如果不支持，可能需要其他方法
                        res = [model(d).cpu().numpy() for d in data]
                else:
                    # MobileFaceNet模型
                    res = [model(d).cpu().numpy() for d in data]
            
            # 连接特征
            featureL = np.concatenate((res[0], res[1]), 1)
            featureR = np.concatenate((res[2], res[3]), 1)
            
            # 累积特征
            if featureLs is None:
                featureLs = featureL
                featureRs = featureR
            else:
                featureLs = np.concatenate((featureLs, featureL), 0)
                featureRs = np.concatenate((featureRs, featureR), 0)
    
    test_time = time.time() - start_time
    
    if logger:
        logger.info(f"特征提取完成, 耗时: {test_time:.2f}秒")
        logger.info(f"特征形状: {featureLs.shape}")
    
    # 保存结果
    result_dir = './result'
    os.makedirs(result_dir, exist_ok=True)
    result_file = os.path.join(result_dir, f'lfw_result_{model_type}.mat')
    
    result = {
        'fl': featureLs, 
        'fr': featureRs, 
        'fold': folds, 
        'flag': flags
    }
    
    scipy.io.savemat(result_file, result)
    
    if logger:
        logger.info(f"特征已保存至: {result_file}")
        logger.info("开始10折交叉验证...")
    
    # 执行10折交叉验证
    accs = evaluation_10_fold(result, logger)
    mean_acc = np.mean(accs) * 100
    
    if logger:
        logger.info(f"LFW最终准确率: {mean_acc:.4f}%")
    
    return mean_acc

def load_model(model_path, model_type, num_classes, device):
    """加载指定类型的模型"""
    if model_type == 'mobilefacenet' or model_type == 'mobilefacenet_cbam':
        # 导入MobileFaceNet模型
        from core import model as face_model
        net = face_model.MobileFacenet().to(device)
    elif model_type == 'mobilefacenet_baseline':
        # 导入MobileFaceNet基准模型
        from core import model as face_model
        net = face_model.MobileFacenetBaseline().to(device)
    elif model_type == 'arcface':
        # 导入ArcFace模型
        from simple_arcface_resnet50 import create_simple_arcface_resnet50
        net = create_simple_arcface_resnet50(num_classes=num_classes).to(device)
    else:
        raise ValueError(f"不支持的模型类型: {model_type}")
    
    # 加载模型权重
    checkpoint = torch.load(model_path, map_location=device)
    
    # 处理不同的检查点格式
    if 'net_state_dict' in checkpoint:
        net.load_state_dict(checkpoint['net_state_dict'])
    elif 'model_state_dict' in checkpoint:
        net.load_state_dict(checkpoint['model_state_dict'])
    else:
        # 尝试直接加载
        net.load_state_dict(checkpoint)
    
    return net

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='测试人脸识别模型在LFW数据集上的性能')
    parser.add_argument('--model_path', type=str, required=True, help='模型检查点路径')
    parser.add_argument('--model_type', type=str, default='mobilefacenet', 
                        choices=['mobilefacenet', 'mobilefacenet_baseline', 'mobilefacenet_cbam', 'arcface'], 
                        help='模型类型')
    parser.add_argument('--num_classes', type=int, default=10572, help='类别数量（仅用于ArcFace模型）')
    parser.add_argument('--output_dir', type=str, default='./test_results', help='测试结果输出目录')
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 设置日志
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_filename = f"{args.model_type}_lfw_test_{timestamp}.txt"
    logger, log_path = setup_logger(args.output_dir, log_filename)
    
    logger.info(f"启动LFW测试")
    logger.info(f"模型路径: {args.model_path}")
    logger.info(f"模型类型: {args.model_type}")
    logger.info(f"测试结果将保存至: {log_path}")
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    try:
        # 加载模型
        logger.info("正在加载模型...")
        net = load_model(args.model_path, args.model_type, args.num_classes, device)
        logger.info("模型加载成功")
        
        # 准备LFW数据集
        logger.info("正在准备LFW数据集...")
        testloader, folds, flags = prepare_lfw_dataset()
        logger.info(f"LFW数据集准备完成, 共{len(testloader.dataset)}对图像")
        
        # 在LFW上测试
        acc = test_on_lfw(net, testloader, folds, flags, device, args.model_type, logger)
        
        # 保存最终结果
        logger.info(f"测试完成! 最终LFW准确率: {acc:.4f}%")
        logger.info(f"详细结果已保存至: {log_path}")
        
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main() 