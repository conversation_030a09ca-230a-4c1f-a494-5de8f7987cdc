#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用示例图像生成随机擦除遮挡样本脚本
生成与visualizations文件夹中其他图片格式相同的随机擦除效果图

作者: AI Assistant
创建时间: 2024
"""

import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'  # 避免OpenMP警告
import numpy as np
import cv2
import matplotlib.pyplot as plt
import torchvision.transforms as transforms
from PIL import Image
import random
from pathlib import Path
import argparse

def create_demo_image():
    """创建演示用的人脸图像"""
    # 创建一个简单的人脸模拟图像
    img = np.ones((112, 112, 3), dtype=np.uint8) * 128
    
    # 绘制简单的人脸轮廓
    center = (56, 56)
    
    # 脸部轮廓
    cv2.circle(img, center, 40, (200, 180, 160), -1)
    
    # 眼睛
    cv2.circle(img, (40, 45), 5, (50, 50, 50), -1)
    cv2.circle(img, (72, 45), 5, (50, 50, 50), -1)
    
    # 鼻子
    cv2.line(img, (56, 50), (56, 65), (150, 130, 110), 2)
    
    # 嘴巴
    cv2.ellipse(img, (56, 75), (8, 4), 0, 0, 180, (100, 80, 80), 2)
    
    return img

def apply_random_erasing(image, p=1.0, scale=(0.02, 0.33), ratio=(0.3, 3.3), value=0):
    """
    使用torchvision的RandomErasing方法为图像添加随机遮挡
    
    参数:
        image: 输入图像 (numpy数组)
        p: 应用擦除的概率
        scale: 擦除区域相对于输入图像的面积比例范围
        ratio: 擦除区域的宽高比范围
        value: 填充值，默认为0（黑色）
    
    返回:
        应用随机擦除后的图像
    """
    # 将numpy图像转换为PIL图像
    pil_img = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    
    # 创建随机擦除转换
    random_erase = transforms.RandomErasing(
        p=p,
        scale=scale,
        ratio=ratio,
        value=value
    )
    
    # 将图像转换为张量
    to_tensor = transforms.ToTensor()
    img_tensor = to_tensor(pil_img)
    
    # 应用随机擦除
    erased_tensor = random_erase(img_tensor)
    
    # 将张量转回PIL图像
    to_pil = transforms.ToPILImage()
    erased_pil = to_pil(erased_tensor)
    
    # 转换回OpenCV格式并返回
    return cv2.cvtColor(np.array(erased_pil), cv2.COLOR_RGB2BGR)

def generate_random_erasing_samples(output_dir=None, num_samples=6):
    """
    生成随机擦除遮挡效果的样本图像
    
    参数:
        output_dir: 输出目录路径，默认为comparison_results/visualizations
        num_samples: 样本数量
    """
    print("🚀 开始生成随机擦除遮挡样本...")
    
    # 设置默认输出目录
    if output_dir is None:
        output_dir = os.path.join(Path(__file__).parent, "comparison_results", "visualizations")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置随机种子以确保结果可重复
    random.seed(42)
    
    # 创建示例图像
    original_img = create_demo_image()
    
    # 创建子图
    fig, axes = plt.subplots(num_samples, 3, figsize=(12, 4*num_samples))
    fig.suptitle('随机擦除遮挡场景变换效果', fontsize=16)
    
    for i in range(num_samples):
        # 创建一个新的示例图像
        img1 = original_img.copy()
        img2 = original_img.copy()
        
        # 应用随机擦除
        img1_transformed = apply_random_erasing(
            img1,
            p=1.0,
            scale=(0.02, 0.33),
            ratio=(0.3, 3.3)
        )
        
        # RGB转换用于显示
        img1_rgb = cv2.cvtColor(img1, cv2.COLOR_BGR2RGB)
        img2_rgb = cv2.cvtColor(img2, cv2.COLOR_BGR2RGB)
        img1_transformed_rgb = cv2.cvtColor(img1_transformed, cv2.COLOR_BGR2RGB)
        
        # 显示图像
        axes[i, 0].imshow(img1_rgb)
        axes[i, 0].set_title('原始图像1')
        axes[i, 0].axis('off')
        
        axes[i, 1].imshow(img1_transformed_rgb)
        axes[i, 1].set_title('变换后图像1')
        axes[i, 1].axis('off')
        
        axes[i, 2].imshow(img2_rgb)
        axes[i, 2].set_title('原始图像2')
        axes[i, 2].axis('off')
    
    plt.tight_layout()
    
    # 保存图像
    output_path = os.path.join(output_dir, "scene_samples_random_erasing.png")
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"\n✅ 随机擦除遮挡样本已生成: {output_path}")
    
    plt.close()
    return output_path

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='生成随机擦除遮挡样本')
    parser.add_argument('--output_dir', type=str, default=None, help='输出目录路径')
    parser.add_argument('--num_samples', type=int, default=6, help='样本数量')
    
    args = parser.parse_args()
    
    try:
        output_path = generate_random_erasing_samples(
            args.output_dir,
            args.num_samples
        )
        print(f"🎉 完成! 图像已保存至: {output_path}")
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        import sys
        sys.exit(1)

if __name__ == "__main__":
    main() 