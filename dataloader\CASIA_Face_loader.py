from PIL import Image
import numpy as np
import os
import torch
from torch.utils.data import Dataset
import random
from collections import defaultdict
from typing import Optional, Callable, List, Tuple
import logging
from tqdm import tqdm
import torch.utils.data as data
from torchvision import transforms
import cv2

class CASIA_Face(data.Dataset):
    """CASIA-WebFace数据集加载器
    
    Args:
        root (str): 数据集根目录
        transform (Optional[transforms.Compose]): 数据增强转换
        is_train (bool): 是否为训练模式（已废弃，保留参数兼容性）
        processed_dir (Optional[str]): 处理后的数据集目录，如果提供则优先使用
        split (str): 数据集划分，'train'或'val'
        val_ratio (float): 验证集比例，默认0.2
    """
    def __init__(self, root: str, transform: Optional[transforms.Compose] = None, 
                 is_train: bool = True, processed_dir: Optional[str] = None,
                 split: str = 'train', val_ratio: float = 0.2):
        self.transform = transform
        self.root = root
        self.processed_dir = processed_dir
        self.split = split
        self.val_ratio = val_ratio
        
        # 配置日志
        logging.basicConfig(level=logging.INFO, format='%(message)s')
        self.logger = logging.getLogger(__name__)
        
        # 如果提供了处理后的数据集目录，则优先使用
        if processed_dir and os.path.exists(processed_dir):
            self.logger.info(f"从处理后的目录加载数据集: {processed_dir}")
            self._load_from_processed_dir()
        else:
            self.logger.info(f"从原始目录加载数据集: {root}")
            self._load_from_original_dir()
            
        # 划分训练集和验证集
        self._split_dataset()
            
    def _load_from_processed_dir(self) -> None:
        """从处理后的数据集目录加载数据"""
        # 使用train目录 - 所有数据都当作训练集
        dataset_dir = os.path.join(self.processed_dir, 'train')
        
        if not os.path.exists(dataset_dir):
            raise FileNotFoundError(f"找不到处理后的数据集目录: {dataset_dir}")
        
        # 获取所有类别目录
        class_dirs = [d for d in os.listdir(dataset_dir) if os.path.isdir(os.path.join(dataset_dir, d))]
        class_dirs.sort(key=int)  # 确保类别ID按数字排序
        
        self.class_nums = len(class_dirs)
        self.classes = {i: int(class_id) for i, class_id in enumerate(class_dirs)}
        
        # 收集所有图片路径和标签
        self.imgs = []
        self.labels = []
        
        for class_idx, class_dir in enumerate(class_dirs):
            class_path = os.path.join(dataset_dir, class_dir)
            for img_name in os.listdir(class_path):
                if img_name.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                    img_path = os.path.join(class_path, img_name)
                    self.imgs.append(img_path)
                    self.labels.append(class_idx)
        
        # 检查数据集大小
        if len(self.imgs) == 0:
            raise ValueError(f"数据集为空: {dataset_dir}")
        
        self.logger.info(f"数据集类别数量: {self.class_nums}")
        self.logger.info(f"数据集样本数量: {len(self.imgs)}")
        
    def _load_from_original_dir(self) -> None:
        """从原始CASIA-WebFace数据集目录加载数据"""
        # 使用训练索引文件
        txt_path = os.path.join(self.root, 'CASIA-WebFace-112X96-train.txt')
        
        if not os.path.exists(txt_path):
            raise FileNotFoundError(f"找不到数据集索引文件: {txt_path}")
        
        # 读取索引文件
        with open(txt_path, 'r') as f:
            img_label_list = f.read().splitlines()
        
        # 解析索引文件
        self.imgs = []
        self.labels = []
        label_set = set()
        
        for info in img_label_list:
            img_path, label = info.split(' ')
            self.imgs.append(os.path.join(self.root, 'CASIA-WebFace-112X96', img_path))
            label_id = int(label)
            self.labels.append(label_id)
            label_set.add(label_id)

        # 构建类别映射
        self.class_nums = len(label_set)
        label_list = sorted(list(label_set))
        self.classes = {i: label for i, label in enumerate(label_list)}
        
        # 检查数据集大小
        if len(self.imgs) == 0:
            raise ValueError(f"数据集为空: {txt_path}")
        
        self.logger.info(f"数据集类别数量: {self.class_nums}")
        self.logger.info(f"数据集样本数量: {len(self.imgs)}")
        
    def _split_dataset(self) -> None:
        """划分训练集和验证集
        
        按照类别进行分层抽样，确保每个类别都有训练和验证样本
        """
        # 按类别分组所有样本
        samples_by_class = defaultdict(list)
        for idx, label in enumerate(self.labels):
            samples_by_class[label].append(idx)
            
        train_indices = []
        val_indices = []
        
        # 为每个类别划分样本
        for label, indices in samples_by_class.items():
            # 打乱样本顺序
            random.shuffle(indices)
            # 计算验证集大小
            val_size = max(1, int(len(indices) * self.val_ratio))
            # 划分
            val_indices.extend(indices[:val_size])
            train_indices.extend(indices[val_size:])
        
        # 保存总样本数
        self.total_samples = len(self.imgs)
        
        # 根据split类型选择相应的索引
        if self.split == 'train':
            self.split_indices = train_indices
            self.logger.info(f"训练集样本数量: {len(self.split_indices)}")
        else:
            self.split_indices = val_indices
            self.logger.info(f"验证集样本数量: {len(self.split_indices)}")

    def __getitem__(self, index: int) -> Tuple[torch.Tensor, int]:
        """获取数据集中的一个样本
        
        Args:
            index (int): 样本索引
            
        Returns:
            tuple: (图像张量, 类别标签)
        """
        try:
            # 使用划分后的索引
            real_index = self.split_indices[index]
            img_path = self.imgs[real_index]
            label = self.labels[real_index]
            
            # 读取图像
            try:
                img = cv2.imread(img_path)
                if img is None:
                    # 如果OpenCV读取失败，尝试使用PIL
                    img = Image.open(img_path).convert('RGB')
                    img = np.array(img)
                    img = img[:, :, ::-1]  # RGB->BGR
            except Exception as e:
                self.logger.warning(f"无法读取图像 {img_path}: {e}")
                # 返回一个随机图像和标签
                return self.__getitem__((index + 1) % len(self))
            
            # 转换为RGB
            img = img[:, :, ::-1]  # BGR->RGB

            # 应用数据增强
            if self.transform is not None:
                img_pil = Image.fromarray(img)
                img_tensor = self.transform(img_pil)  # 确保transform返回tensor
                return img_tensor, label
            else:
                # 如果没有提供transform，执行基本的转换
                img = torch.from_numpy(img.transpose((2, 0, 1)))
                img = img.float().div(255)
                return img, label
                
        except Exception as e:
            self.logger.error(f"处理样本 {index} 时出错: {e}")
            # 返回一个随机样本
            return self.__getitem__((index + 1) % len(self))

    def __len__(self) -> int:
        """返回数据集大小"""
        return len(self.split_indices)


if __name__ == '__main__':
    # 测试代码
    from config import CASIA_DATA_DIR, PROCESSED_CASIA_DIR
    
    # 创建数据转换
    test_transform = transforms.Compose([
        transforms.RandomHorizontalFlip(),
        transforms.ToTensor(),  # 确保转换为tensor
        transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
    ])
    
    # 创建训练集
    dataset = CASIA_Face(root='', transform=test_transform, processed_dir=PROCESSED_CASIA_DIR)
    
    # 创建数据加载器
    data_loader = torch.utils.data.DataLoader(dataset, batch_size=32, shuffle=True, num_workers=0)
    
    print(f"数据集大小: {len(dataset)}")
    print(f"类别数量: {dataset.class_nums}")
    
    # 测试数据加载
    for data in data_loader:
        images, labels = data
        print(f"批次形状: {images.shape}")
        print(f"标签形状: {labels.shape}")
        print(f"数据类型: {images.dtype}")
        break
