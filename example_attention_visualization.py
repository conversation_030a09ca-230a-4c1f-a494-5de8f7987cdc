#!/usr/bin/env python3
"""
MobileFaceNet空间注意力可视化示例脚本

此示例脚本展示了如何使用visualize_attention模块可视化人脸图像的空间注意力热力图。
"""

import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'
import torch
import matplotlib.pyplot as plt
from typing import List, Optional

from visualize_attention import load_model, register_hooks, preprocess_image, visualize_attention
from config import GPU

def run_example(image_path: str, 
              model_path: str = 'model/best/068.ckpt',
              save_dir: Optional[str] = 'result/attention_examples') -> None:
    """运行注意力可视化示例
    
    Args:
        image_path: 输入图像路径
        model_path: 模型路径
        save_dir: 保存结果的目录
    """
    # 创建保存目录
    if save_dir:
        os.makedirs(save_dir, exist_ok=True)
        
    # 获取文件名
    file_name = os.path.splitext(os.path.basename(image_path))[0]
    save_path = os.path.join(save_dir, f"{file_name}_attention.png") if save_dir else None
    
    # 设置设备
    if isinstance(GPU, int):
        device = torch.device(f"cuda:{GPU}" if torch.cuda.is_available() else "cpu")
    else:
        device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    
    print(f"使用设备: {device}")
    
    # 加载模型
    model = load_model(model_path, device)
    print(f"已加载模型: {model_path}")
    
    # 注册钩子
    hooks = register_hooks(model)
    print(f"已注册 {len(hooks)} 个空间注意力钩子")
    
    # 预处理图像
    img_tensor, original_image = preprocess_image(image_path, device)
    print(f"已加载图像: {image_path}")
    
    # 前向传播，获取注意力图
    with torch.no_grad():
        features = model(img_tensor)
        print(f"特征向量维度: {features.shape}")
    
    # 收集所有空间注意力图
    attention_maps = {}
    for name, hook in hooks.items():
        attention_maps.update(hook.spatial_maps)
    
    print(f"提取了 {len(attention_maps)} 个空间注意力图")
    
    # 可视化
    visualize_attention(original_image, attention_maps, save_path)
    
    if save_path:
        print(f"结果已保存至: {save_path}")
    else:
        print("结果已显示，未保存")

def main():
    """主函数"""
    # 示例图像路径 - 用户需要替换为自己的图像路径
    image_path = 'data/lfw/lfw-112X96/Abraham_Foxman/Abraham_Foxman_0001.jpg'
    
    # 检查图像是否存在
    if not os.path.exists(image_path):
        print(f"警告: 图像 {image_path} 不存在，请替换为您自己的图像路径")
        return
    
    # 运行示例
    run_example(image_path)
    
    print("\n示例使用说明:")
    print("1. 修改本脚本中的image_path为您自己的图像路径")
    print("2. 或者使用命令行工具处理图像:")
    print("   - 单图像: python visualize_attention.py --image_path <图像路径>")
    print("   - 批量处理: python batch_visualize_attention.py --input_dir <输入目录> --output_dir <输出目录>")

if __name__ == '__main__':
    main() 