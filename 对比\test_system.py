#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比系统功能测试脚本
验证重新设计的对比系统是否正常工作


"""

import sys
import os
from pathlib import Path

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        from comprehensive_model_comparison import ModelComparator, ModelConfig, EvaluationMetrics
        print("   ✅ comprehensive_model_comparison 导入成功")
    except ImportError as e:
        print(f"   ❌ comprehensive_model_comparison 导入失败: {e}")
        return False
    
    try:
        from complex_scene_evaluator import ComplexSceneEvaluator, ComplexSceneTransforms
        print("   ✅ complex_scene_evaluator 导入成功")
    except ImportError as e:
        print(f"   ❌ complex_scene_evaluator 导入失败: {e}")
        return False
    
    return True

def test_dependencies():
    """测试依赖库"""
    print("\n🔍 测试依赖库...")
    
    dependencies = [
        ('torch', 'PyTorch深度学习框架'),
        ('numpy', 'NumPy数值计算库'),
        ('cv2', 'OpenCV计算机视觉库'),
        ('matplotlib', 'Matplotlib绘图库'),
        ('seaborn', 'Seaborn统计绘图库'),
        ('pandas', 'Pandas数据处理库'),
        ('sklearn', 'Scikit-learn机器学习库'),
        ('prettytable', 'PrettyTable表格显示库'),
        ('tqdm', 'TQDM进度条库')
    ]
    
    missing_deps = []
    
    for dep_name, description in dependencies:
        try:
            __import__(dep_name)
            print(f"   ✅ {dep_name} - {description}")
        except ImportError:
            print(f"   ❌ {dep_name} - {description}")
            missing_deps.append(dep_name)
    
    if missing_deps:
        print(f"\n⚠️  缺少依赖库: {', '.join(missing_deps)}")
        print("请使用以下命令安装:")
        print(f"pip install {' '.join(missing_deps)}")
        return False
    
    return True

def test_model_configs():
    """测试模型配置"""
    print("\n🔍 测试模型配置...")
    
    try:
        from comprehensive_model_comparison import ModelComparator
        
        # 创建一个临时的对比器实例
        temp_dir = "./temp_test"
        comparator = ModelComparator("./", temp_dir)
        
        configs = comparator.model_configs
        print(f"   ✅ 发现 {len(configs)} 个模型配置:")
        
        for model_name, config in configs.items():
            print(f"      📌 {model_name} ({config.model_type}): {config.description}")
        
        # 清理临时目录
        import shutil
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
        
        return True
        
    except Exception as e:
        print(f"   ❌ 模型配置测试失败: {e}")
        return False

def test_scene_transforms():
    """测试场景变换功能"""
    print("\n🔍 测试场景变换功能...")
    
    try:
        from complex_scene_evaluator import ComplexSceneTransforms
        import numpy as np
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (112, 112, 3), dtype=np.uint8)
        
        transforms = [
            ('低光照', ComplexSceneTransforms.simulate_low_light),
            ('高光/逆光', ComplexSceneTransforms.simulate_high_light),
            ('运动模糊', ComplexSceneTransforms.simulate_motion_blur),
            ('高斯模糊', ComplexSceneTransforms.simulate_gaussian_blur),
            ('遮挡', ComplexSceneTransforms.simulate_occlusion),
            ('极端姿态', ComplexSceneTransforms.simulate_extreme_pose),
            ('低分辨率', ComplexSceneTransforms.simulate_low_resolution)
        ]
        
        for name, transform_func in transforms:
            try:
                if name == '低光照':
                    result = transform_func(test_image.copy(), 0.3)
                elif name == '高光/逆光':
                    result = transform_func(test_image.copy(), 2.0)
                elif name in ['运动模糊', '高斯模糊']:
                    result = transform_func(test_image.copy(), 5)
                elif name == '遮挡':
                    result = transform_func(test_image.copy(), 0.3)
                elif name == '极端姿态':
                    result = transform_func(test_image.copy(), (-30, 30))
                elif name == '低分辨率':
                    result = transform_func(test_image.copy(), 0.3)
                else:
                    result = transform_func(test_image.copy())
                
                print(f"   ✅ {name}变换测试成功")
                
            except Exception as e:
                print(f"   ❌ {name}变换测试失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 场景变换测试失败: {e}")
        return False

def test_evaluation_metrics():
    """测试评估指标数据类"""
    print("\n🔍 测试评估指标数据类...")
    
    try:
        from comprehensive_model_comparison import EvaluationMetrics
        
        # 创建测试指标
        metrics = EvaluationMetrics(
            accuracy=95.5,
            roc_auc=0.998,
            eer=2.1,
            tar_at_far_1e3=98.5,
            tar_at_far_1e4=96.2,
            tar_at_far_1e5=93.8,
            inference_time_ms=15.2,
            batch_inference_time_ms=45.6,
            fps=65.8,
            model_size_mb=12.3,
            param_count_m=4.2,
            flops_g=1.8,
            low_light_acc=85.0,
            high_light_acc=88.0,
            blur_acc=82.0,
            occlusion_acc=80.0,
            extreme_pose_acc=78.0,
            low_resolution_acc=83.0
        )
        
        print(f"   ✅ 评估指标创建成功")
        print(f"      准确率: {metrics.accuracy}%")
        print(f"      AUC: {metrics.roc_auc}")
        print(f"      推理时间: {metrics.inference_time_ms}ms")
        print(f"      模型大小: {metrics.model_size_mb}MB")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 评估指标测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试人脸识别模型综合对比评估系统")
    print("="*60)
    
    tests = [
        ("模块导入", test_imports),
        ("依赖库", test_dependencies),
        ("模型配置", test_model_configs),
        ("场景变换", test_scene_transforms),
        ("评估指标", test_evaluation_metrics)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✅ {test_name}测试通过")
            else:
                print(f"\n❌ {test_name}测试失败")
        except Exception as e:
            print(f"\n❌ {test_name}测试异常: {e}")
    
    print("\n" + "="*60)
    print(f"📊 测试结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常使用")
        print("\n📋 下一步:")
        print("   1. 准备LFW数据集")
        print("   2. 运行: python run_comprehensive_comparison.py --lfw_dir /path/to/lfw")
        print("   3. 查看生成的对比报告")
    else:
        print("⚠️  部分测试失败，请检查相关问题")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1) 