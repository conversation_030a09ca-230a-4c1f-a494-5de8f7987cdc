#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
随机擦除遮挡可视化脚本
展示使用torchvision的RandomErasing方法为图像添加随机遮挡的效果

作者: AI Assistant
创建时间: 2024
"""

import numpy as np
import cv2
import matplotlib.pyplot as plt
from complex_scene_evaluator import ComplexSceneTransforms, ComplexSceneEvaluator
import os
import argparse
from pathlib import Path
import time
import random
from PIL import Image
import torchvision.transforms as transforms

def create_demo_image():
    """创建演示用的人脸图像"""
    # 创建一个简单的人脸模拟图像
    img = np.ones((112, 112, 3), dtype=np.uint8) * 128
    
    # 绘制简单的人脸轮廓
    center = (56, 56)
    
    # 脸部轮廓
    cv2.circle(img, center, 40, (200, 180, 160), -1)
    
    # 眼睛
    cv2.circle(img, (40, 45), 5, (50, 50, 50), -1)
    cv2.circle(img, (72, 45), 5, (50, 50, 50), -1)
    
    # 鼻子
    cv2.line(img, (56, 50), (56, 65), (150, 130, 110), 2)
    
    # 嘴巴
    cv2.ellipse(img, (56, 75), (8, 4), 0, 0, 180, (100, 80, 80), 2)
    
    return img

def visualize_random_erasing_examples():
    """可视化不同参数下的随机擦除遮挡效果"""
    print("🎨 开始随机擦除遮挡变换演示...")
    
    # 创建演示图像
    original_img = create_demo_image()
    
    # 不同的随机擦除参数组合
    erasing_params = [
        {
            'title': '小区域遮挡',
            'p': 1.0, 
            'scale': (0.02, 0.1), 
            'ratio': (0.5, 2.0),
            'value': 0
        },
        {
            'title': '中等区域遮挡',
            'p': 1.0, 
            'scale': (0.1, 0.25), 
            'ratio': (0.5, 2.0),
            'value': 0
        },
        {
            'title': '大区域遮挡',
            'p': 1.0, 
            'scale': (0.25, 0.5), 
            'ratio': (0.5, 2.0),
            'value': 0
        },
        {
            'title': '横向长条遮挡',
            'p': 1.0, 
            'scale': (0.1, 0.25), 
            'ratio': (3.0, 5.0),
            'value': 0
        },
        {
            'title': '纵向长条遮挡',
            'p': 1.0, 
            'scale': (0.1, 0.25), 
            'ratio': (0.2, 0.33),
            'value': 0
        }
    ]
    
    # 设置随机种子使结果可重现
    random.seed(42)
    
    # 创建子图
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle('RandomErasing随机擦除遮挡效果', fontsize=16, fontweight='bold')
    
    # 显示原始图像
    axes[0, 0].imshow(cv2.cvtColor(original_img, cv2.COLOR_BGR2RGB))
    axes[0, 0].set_title('原始图像', fontsize=12)
    axes[0, 0].axis('off')
    
    # 显示不同参数下的随机擦除效果
    for i, params in enumerate(erasing_params):
        row = (i + 1) // 3
        col = (i + 1) % 3
        
        # 生成随机擦除效果
        transformed_img = ComplexSceneTransforms.simulate_random_erasing(
            original_img.copy(),
            p=params['p'],
            scale=params['scale'],
            ratio=params['ratio'],
            value=params['value']
        )
        
        # 显示图像
        axes[row, col].imshow(cv2.cvtColor(transformed_img, cv2.COLOR_BGR2RGB))
        axes[row, col].set_title(params['title'], fontsize=12)
        axes[row, col].axis('off')
        
        print(f"   ✅ {params['title']}变换完成")
    
    plt.tight_layout()
    
    # 保存图像
    output_path = './random_erasing_demo.png'
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"\n📁 演示图像已保存至: {output_path}")
    
    # 关闭图像
    plt.close()

def visualize_comparison_with_occlusion():
    """比较传统遮挡和随机擦除遮挡的区别"""
    print("\n🔄 比较传统遮挡与随机擦除遮挡...")
    
    # 创建示例图像
    original_img = create_demo_image()
    
    # 获取10个样本进行比较
    num_samples = 5
    samples = []
    
    # 设置随机种子
    random.seed(42)
    
    # 生成多个样本
    for i in range(num_samples):
        # 传统遮挡
        traditional_occluded = ComplexSceneTransforms.simulate_occlusion(
            original_img.copy(), occlusion_ratio=0.3)
        
        # 随机擦除遮挡
        random_erased = ComplexSceneTransforms.simulate_random_erasing(
            original_img.copy(), p=1.0, scale=(0.02, 0.33), ratio=(0.3, 3.3), value=0)
        
        samples.append((traditional_occluded, random_erased))

    # 创建子图
    fig, axes = plt.subplots(num_samples, 3, figsize=(12, num_samples * 4))
    fig.suptitle('传统遮挡 vs 随机擦除遮挡', fontsize=16, fontweight='bold')
    
    # 显示原始图像在第一行
    axes[0, 0].imshow(cv2.cvtColor(original_img, cv2.COLOR_BGR2RGB))
    axes[0, 0].set_title('原始图像', fontsize=12)
    axes[0, 0].axis('off')
    
    for i, (traditional, random_erased) in enumerate(samples):
        # 显示传统遮挡
        axes[i, 1].imshow(cv2.cvtColor(traditional, cv2.COLOR_BGR2RGB))
        axes[i, 1].set_title(f'传统遮挡 #{i+1}', fontsize=12)
        axes[i, 1].axis('off')
        
        # 显示随机擦除
        axes[i, 2].imshow(cv2.cvtColor(random_erased, cv2.COLOR_BGR2RGB))
        axes[i, 2].set_title(f'随机擦除 #{i+1}', fontsize=12)
        axes[i, 2].axis('off')
    
    plt.tight_layout()
    
    # 保存图像
    output_path = './comparison_results/visualizations/scene_samples_occlusion_comparison.png'
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"   📁 对比图像已保存至: {output_path}")
    
    # 关闭图像
    plt.close()

def visualize_with_real_images(lfw_dir=None):
    """使用真实人脸图像进行可视化"""
    if not lfw_dir:
        print("⚠️ 未提供LFW数据集路径，跳过真实图像可视化")
        return
    
    print("\n🧑‍🦰 使用真实人脸图像进行可视化...")
    
    try:
        # 创建评估器实例
        evaluator = ComplexSceneEvaluator(lfw_dir)
        
        # 可视化随机擦除场景
        output_path = './comparison_results/visualizations/scene_samples_random_erasing.png'
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        evaluator.visualize_scene_samples(
            'random_erasing', 
            num_samples=6,
            save_path=output_path
        )
        
        print(f"   ✅ 真实图像随机擦除效果已保存至: {output_path}")
    except Exception as e:
        print(f"   ❌ 真实图像可视化失败: {e}")

def main():
    """主函数"""
    print("🚀 随机擦除遮挡可视化演示")
    print("="*50)
    
    parser = argparse.ArgumentParser(description='随机擦除遮挡可视化')
    parser.add_argument('--lfw_dir', type=str, default=None, help='LFW数据集路径')
    args = parser.parse_args()
    
    # 可视化随机擦除的不同参数效果
    visualize_random_erasing_examples()
    
    # 比较传统遮挡和随机擦除的效果
    visualize_comparison_with_occlusion()
    
    # 使用真实图像进行可视化
    if args.lfw_dir:
        visualize_with_real_images(args.lfw_dir)
    
    print("\n🎉 可视化完成!")
    print("📋 生成的文件:")
    print("   • random_erasing_demo.png - 不同参数随机擦除效果")
    print("   • comparison_results/visualizations/scene_samples_occlusion_comparison.png - 传统遮挡vs随机擦除")
    if args.lfw_dir:
        print("   • comparison_results/visualizations/scene_samples_random_erasing.png - 真实人脸随机擦除效果")

if __name__ == '__main__':
    main() 