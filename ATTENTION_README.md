# MobileFaceNet空间注意力可视化工具

此工具用于可视化MobileFaceNet+CBAM模型中的空间注意力机制。通过生成热力图，可以直观地观察模型在人脸识别过程中关注的区域。

## 功能特点

- 提取MobileFaceNet+CBAM模型中的空间注意力图
- 生成原始图像与空间注意力热力图对比
- 支持单张图像处理和批量图像处理
- 可视化结果可保存为PNG格式

## 运行环境要求

- Python 3.6+
- PyTorch 1.7+
- torchvision
- matplotlib
- numpy
- PIL (Pillow)
- opencv-python (cv2)
- tqdm

## 使用方法

### 单张图像处理

用于处理单张图像并可视化其注意力热力图：

```bash
python visualize_attention.py --image_path <图像路径> --model_path <模型路径> --save_path <保存路径>
```

参数说明：
- `--image_path`: 必需，输入图像的路径
- `--model_path`: 可选，预训练模型的路径，默认为"model/best/MobileFaceNet_CBAM_best.ckpt"
- `--save_path`: 可选，保存可视化结果的路径，不指定则显示图像不保存

### 批量图像处理

用于批量处理文件夹中的所有图像：

```bash
python batch_visualize_attention.py --input_dir <输入目录> --output_dir <输出目录> --model_path <模型路径>
```

参数说明：
- `--input_dir`: 必需，输入图像目录
- `--output_dir`: 必需，输出可视化结果目录
- `--model_path`: 可选，预训练模型的路径，默认为"model/best/MobileFaceNet_CBAM_best.ckpt"

## 示例

处理单张图像：

```bash
python visualize_attention.py --image_path data/test/face1.jpg --save_path result/face1_attention.png
```

批量处理图像：

```bash
python batch_visualize_attention.py --input_dir data/test_faces --output_dir result/attention_maps
```

## 结果说明

生成的可视化结果包括：
1. 原始输入图像
2. 多个空间注意力热力图（每个热力图对应模型中不同位置的空间注意力机制）

热力图颜色含义：
- 红色区域：模型高度关注的区域
- 蓝色区域：模型较少关注的区域

## 注意事项

1. 确保输入图像为人脸图像，并且分辨率适合模型处理（建议尺寸大于112x96）
2. 运行脚本前请确认预训练模型路径正确
3. 对于大量图像的批处理，建议使用GPU加速 