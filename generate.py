# -*- coding: utf-8 -*-
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN

# --- Helper Functions for Styling ---

def set_slide_background(slide, rgb_color):
    """设置幻灯片的纯色背景"""
    fill = slide.background.fill
    fill.solid()
    fill.fore_color.rgb = rgb_color

def add_title(slide, text, left=Inches(0.5), top=Inches(0.3), width=Inches(9), height=Inches(1)):
    """向幻灯片添加标题文本框"""
    title_box = slide.shapes.add_textbox(left, top, width, height)
    tf = title_box.text_frame
    p = tf.paragraphs[0]
    p.text = text
    p.font.name = '微软雅黑'
    p.font.size = Pt(36)
    p.font.bold = True
    p.font.color.rgb = RGBColor(255, 255, 255) # White text
    p.alignment = PP_ALIGN.LEFT

def add_content(slide, text_items, left=Inches(0.7), top=Inches(1.5), width=Inches(8.6), height=Inches(5.5), level=0):
    """向幻灯片添加内容文本框（支持多级列表）"""
    content_box = slide.shapes.add_textbox(left, top, width, height)
    tf = content_box.text_frame
    tf.word_wrap = True
    
    for item in text_items:
        if isinstance(item, str):
            p = tf.add_paragraph()
            p.text = item
            p.font.name = '微软雅黑'
            p.font.size = Pt(22)
            p.font.color.rgb = RGBColor(220, 220, 220) # Light Gray text
            p.level = 0
            p.line_spacing = 1.2
        elif isinstance(item, list): # For sub-bullets
            for sub_item in item:
                p = tf.add_paragraph()
                p.text = sub_item
                p.font.name = '微软雅黑'
                p.font.size = Pt(19)
                p.font.color.rgb = RGBColor(200, 200, 200) # Slightly dimmer gray
                p.level = 1
                p.line_spacing = 1.2
                
def add_image_placeholder(slide, left, top, width, height, text="[ 此处插入图片/图表 ]"):
    """添加图片占位符"""
    placeholder = slide.shapes.add_textbox(left, top, width, height)
    
    # Set shape fill and line
    fill = placeholder.fill
    fill.solid()
    fill.fore_color.rgb = RGBColor(60, 60, 60) # Darker gray fill
    
    line = placeholder.line
    line.color.rgb = RGBColor(128, 128, 128)
    line.width = Pt(1.5)

    # Set text
    tf = placeholder.text_frame
    p = tf.paragraphs[0]
    p.text = text
    p.font.name = '微软雅黑'
    p.font.size = Pt(18)
    p.font.color.rgb = RGBColor(200, 200, 200)
    p.alignment = PP_ALIGN.CENTER
    tf.vertical_anchor = 'middle'


# --- Main Presentation Creation ---

# Create a new presentation object
prs = Presentation()
prs.slide_width = Inches(10)
prs.slide_height = Inches(7.5)

# Define a modern, dark background color
dark_bg_color = RGBColor(34, 40, 49) # A dark slate blue/gray

# --- Slide 1: Title Slide ---
slide_layout = prs.slide_layouts[6] # Blank layout
slide = prs.slides.add_slide(slide_layout)
set_slide_background(slide, dark_bg_color)

# Title
title_box = slide.shapes.add_textbox(Inches(0.5), Inches(2.0), Inches(9), Inches(1.5))
tf = title_box.text_frame
p = tf.paragraphs[0]
p.text = "基于MobileFaceNet改进的人脸识别算法设计与应用"
p.font.name = '微软雅黑'
p.font.size = Pt(40)
p.font.bold = True
p.font.color.rgb = RGBColor(0, 173, 181) # A vibrant teal color for title
p.alignment = PP_ALIGN.LEFT

# Subtitle
subtitle_box = slide.shapes.add_textbox(Inches(0.5), Inches(3.5), Inches(9), Inches(2))
tf = subtitle_box.text_frame
p = tf.add_paragraph()
p.text = "硕士学位论文中期汇报"
p.font.name = '微软雅黑'
p.font.size = Pt(24)
p.font.color.rgb = RGBColor(238, 238, 238)

p = tf.add_paragraph()
p.text = "\n汇报人：张珍奇\n指导老师：徐敏  陈显"
p.font.name = '微软雅黑'
p.font.size = Pt(18)
p.font.color.rgb = RGBColor(220, 220, 220)

# --- Slide 2: Research Background & Challenges ---
slide = prs.slides.add_slide(slide_layout)
set_slide_background(slide, dark_bg_color)
add_title(slide, "01. 研究背景与挑战")
add_content(slide, [
    "影视制作场景对人脸识别技术提出严苛要求，是技术应用的“试金石”。",
    "核心挑战:",
    [
        "动态光照：强弱、色彩变化导致面部特征不稳定。",
        "多变角度：非正面、大姿态下识别难度剧增。",
        "复杂妆造：特效妆、年代妆等严重改变面部外观。",
    ],
    "研究目标：设计一个轻量级、高精度且鲁棒的人脸识别模型，满足影视后期制作的实际需求。"
], top=Inches(1.5), width=Inches(5.5))
add_image_placeholder(slide, Inches(6.5), Inches(2.0), Inches(3), Inches(4.5), "[ 插入影视剧照\n（光照、角度、妆造变化示例）]")

# --- Slide 3: Core Model Design ---
slide = prs.slides.add_slide(slide_layout)
set_slide_background(slide, dark_bg_color)
add_title(slide, "02. 核心模型设计: AM-MobileFaceNet")
add_content(slide, [
    "基础网络：MobileFaceNet (兼具高效性与低功耗)。",
    "创新融合：引入卷积块注意力模块 (CBAM)，实现“智能聚焦”。",
    "工作原理:",
    [
        "通道注意力：学习特征通道的重要性，抑制光照/肤色等全局干扰。",
        "空间注意力：定位面部关键区域（如五官），减少妆造等局部遮挡干扰。",
    ],
    "最终目标：在保持轻量化的同时，引导模型将有限的计算资源用于处理最关键的信息，实现性能与效率的更优平衡。"
], top=Inches(1.5), width=Inches(5.5))
add_image_placeholder(slide, Inches(6.5), Inches(2.0), Inches(3), Inches(4.5), "[ 插入AM-MobileFaceNet\n模型结构图 ]")

# --- Slide 4: Experimental Validation & Results ---
slide = prs.slides.add_slide(slide_layout)
set_slide_background(slide, dark_bg_color)
add_title(slide, "03. 实验验证与结果")
add_content(slide, [
    "实验一：模型综合性能验证",
    [
        "数据集：LFW (国际标准人脸数据集)。",
        "结论：与原始MobileFaceNet等基线模型相比，本模型在识别精度上实现显著提升，同时保持了轻量级的速度优势。"
    ],
    "实验二：特定场景鲁棒性评估",
    [
        "数据集：公开的跨文化、妆造变化人脸数据集。",
        "结论：注意力机制有效帮助模型克服肤色、妆容等干扰，验证了模型在影视场景下的实际应用价值。"
    ]
], top=Inches(1.5), width=Inches(9))
add_image_placeholder(slide, Inches(0.7), Inches(4.8), Inches(4), Inches(2.2), "[ 插入性能对比图表\n(准确率/速度) ]")
add_image_placeholder(slide, Inches(5.3), Inches(4.8), Inches(4), Inches(2.2), "[ 插入妆造/跨文化识别成功案例 ]")


# --- Slide 5: Interim Achievements ---
slide = prs.slides.add_slide(slide_layout)
set_slide_background(slide, dark_bg_color)
add_title(slide, "04. 阶段性成果与新见解")
add_content(slide, [
    "主要成果:",
    [
        "成功构建了改进的轻量级人脸识别模型 AM-MobileFaceNet。",
        "两篇软件著作权已录用。",
        "一篇 SCI 论文正在投稿中。",
    ],
    "个人见解与思考:",
    [
        "性能与效率的平衡：智能模块 (注意力) 是比盲目加深网络更优的策略。",
        "问题驱动的技术选型：CBAM 的成功源于其机理与特定挑战的高度契合，未来的模型优化应更加注重问题导向。"
    ]
])


# --- Slide 6: Future Work & Plan ---
slide = prs.slides.add_slide(slide_layout)
set_slide_background(slide, dark_bg_color)
add_title(slide, "05. 后期工作安排")
add_content(slide, [
    "问题一：长视频序列中的身份关联与追踪效率问题",
    [
        "研究方案：集成DeepSORT/ByteTrack等追踪算法，构建长时序追踪原型系统，并进行性能评估与优化（如知识蒸馏、模型剪枝）。"
    ],
    "问题二：模型决策的可解释性与应用场景的深度融合",
    [
        "研究方案：开发注意力热力图可视化功能；设计带置信度评分和人工反馈循环的原型交互界面，增强用户信任与模型迭代能力。"
    ]
])

# --- Slide 7: Timeline ---
slide = prs.slides.add_slide(slide_layout)
set_slide_background(slide, dark_bg_color)
add_title(slide, "06. 工作时间线规划")
add_image_placeholder(slide, Inches(0.5), Inches(1.5), Inches(9), Inches(5.5), 
"""
[ 在此处插入Gantt图或时间规划图 ]

示例内容：
方向一：长时序追踪系统 (预计3个月)
- 阶段一：原型系统构建 (2个月)
- 阶段二：性能测试与优化 (1个月)

方向二：模型可解释性 (预计2.5个月)
- 阶段一：可视化研究 (1.5个月)
- 阶段二：人机交互原型开发 (1个月)

论文撰写与完善将贯穿整个后期阶段。
""")


# --- Slide 8: Thank You / Q&A ---
slide = prs.slides.add_slide(slide_layout)
set_slide_background(slide, dark_bg_color)
# Centered Title
title_box = slide.shapes.add_textbox(Inches(0.5), Inches(3.0), Inches(9), Inches(1.5))
tf = title_box.text_frame
p = tf.paragraphs[0]
p.text = "感谢聆听，敬请批评指正！"
p.font.name = '微软雅黑'
p.font.size = Pt(44)
p.font.bold = True
p.font.color.rgb = RGBColor(255, 255, 255)
p.alignment = PP_ALIGN.CENTER


# Save the presentation
file_path = "中期汇报PPT_张珍奇.pptx"
prs.save(file_path)

print(f"PPT '{file_path}' 已成功生成！")