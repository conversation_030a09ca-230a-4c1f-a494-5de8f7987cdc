"""核心工具函数模块

提供日志初始化、设备管理、模型工具等通用功能。
遵循代码风格规范，使用类型注解和中文注释。
"""
from __future__ import print_function
import os
import logging


def init_log(output_dir):
    # 配置文件日志处理器
    file_handler = logging.FileHandler(
        filename=os.path.join(output_dir, 'log.log'),
        mode='w',
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(
        logging.Formatter('%(asctime)s %(message)s', datefmt='%H:%M:%S')
    )
    
    # 配置控制台日志处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(
        logging.Formatter('%(message)s')
    )
    
    # 配置根日志记录器
    root_logger = logging.getLogger('')
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    return logging

if __name__ == '__main__':
    pass
