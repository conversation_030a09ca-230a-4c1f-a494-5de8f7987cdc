---
description: 
globs: 
alwaysApply: false
---
# 训练与评估指南

## 训练流程

### IMFDB数据集训练
使用 [train_imfdb.py](mdc:train_imfdb.py) 进行IMFDB数据集训练：
```bash
python train_imfdb.py
```

## 配置参数

### 主要配置 ([config.py](mdc:config.py))
- `BATCH_SIZE`: 批量大小，默认64
- `TOTAL_EPOCH`: 训练轮数，默认50
- `SAVE_FREQ`: 模型保存频率
- `TEST_FREQ`: 测试频率
- `GPU`: 使用的GPU编号

### 数据集路径配置
- `IMFDB_DATA_DIR`: IMFDB数据集路径
- `CASIA_DATA_DIR`: CASIA数据集路径
- `LFW_DATA_DIR`: LFW测试数据集路径

## 模型评估

### LFW数据集评估
```bash
python lfw_eval.py
```
用于评估模型在LFW人脸验证任务上的性能。

### YouTube Faces评估
```bash
python youtube_faces_eval.py
```
用于评估模型在YouTube Faces数据集上的性能。

## 数据集格式要求

### CASIA数据集
- 使用 [dataloader/CASIA_Face_loader.py](mdc:dataloader/CASIA_Face_loader.py)
- 图像尺寸：112x96
- 格式：JPG/PNG

### IMFDB数据集
- 使用 [dataloader/IMFDB_loader.py](mdc:dataloader/IMFDB_loader.py)
- 支持多种图像格式
- 自动数据增强

### LFW数据集
- 使用 [dataloader/LFW_loader.py](mdc:dataloader/LFW_loader.py)
- 标准LFW格式
- 用于模型验证

## 训练最佳实践
1. **预训练模型**: 设置 `RESUME` 参数加载预训练权重
2. **学习率调整**: 根据训练曲线适当调整学习率
3. **数据增强**: 在数据加载器中启用适当的数据增强
4. **模型保存**: 定期保存检查点，防止训练中断
5. **GPU内存**: 根据GPU内存调整批量大小

## 监控训练进度
- 训练日志会显示损失变化
- 模型自动保存到 `model/` 目录
- 使用测试频率参数控制验证频率

