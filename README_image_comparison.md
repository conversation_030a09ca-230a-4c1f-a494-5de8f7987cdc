# Image Transformation Comparison Scripts

This repository contains scripts to create side-by-side comparisons of image transformations, showing the effects of various image degradations commonly used in computer vision research.

## Generated Files

1. **`final_image_comparison.py`** - Main script that creates the final comparison image
2. **`image_transformation_comparison.py`** - Original version with basic functionality
3. **`enhanced_image_comparison.py`** - Enhanced version with better layout

## Features

The scripts apply the following 6 transformations to compare original vs. transformed images:

1. **Low Resolution** - Simulates low-quality image capture
2. **Focus Blur** - Applies Gaussian blur to simulate out-of-focus images
3. **Motion Blur** - Simulates camera shake or subject movement
4. **Exposure** - Simulates overexposure/bright lighting conditions
5. **Backlight** - Simulates backlighting/underexposure conditions
6. **Random Occlusion** - Adds random dark patches to simulate partial occlusion

## Output Format

The final output image follows this layout:
- **Left Column**: Original images (Image 1 on top, Image 2 on bottom)
- **Right Column**: Transformed images (Image 1 on top, Image 2 on bottom)
- **No gaps** between the two columns
- **English labels** for each transformation group
- **6 rows** total, one for each transformation

## Usage

### Prerequisites

Install required packages:
```bash
pip install pillow
```

### Running the Script

```bash
python final_image_comparison.py
```

### Input Images

The script is configured to use:
- **Image 1**: `data/CASIA/CASIA-WebFace-112X96/0000268/001.jpg`
- **Image 2**: `data/CASIA/CASIA-WebFace-112X96/0002743/002.jpg`

### Output

The script generates `final_comparison.png` in the current directory.

## Customization

### Changing Input Images

Edit the paths in the `if __name__ == "__main__":` section:

```python
img1_path = r"path/to/your/image1.jpg"
img2_path = r"path/to/your/image2.jpg"
```

### Modifying Transformations

You can adjust transformation parameters by modifying the functions:

- `low_resolution_transform()` - Change `scale_factor` (default: 0.3)
- `focus_blur_transform()` - Change `radius` (default: 5)
- `exposure_transform()` - Change brightness enhancement factor (default: 2.2)
- `backlight_transform()` - Change `brightness_factor` (default: 0.3)
- `random_occlusion_transform()` - Change `num_patches` and `patch_size_range`

### Adding New Transformations

To add a new transformation:

1. Create a new function following the pattern:
```python
def new_transform(img):
    # Apply your transformation
    return transformed_img
```

2. Add it to the `transformations` list:
```python
transformations = [
    # ... existing transformations
    ("New Transform", new_transform),
]
```

## Technical Details

- **Image Format**: Supports common formats (JPG, PNG, etc.)
- **Color Space**: All images are converted to RGB
- **Output Quality**: PNG format with 95% quality
- **Font**: Uses Arial if available, falls back to default font
- **Error Handling**: Shows red placeholder if transformation fails

## File Structure

```
.
├── final_image_comparison.py          # Main script (recommended)
├── enhanced_image_comparison.py       # Enhanced version with margins
├── image_transformation_comparison.py # Original basic version
├── final_comparison.png              # Generated output
└── README_image_comparison.md        # This file
```

## Notes

- The script preserves the original image dimensions
- All transformations are applied independently
- Random occlusion uses different random patches each time
- The layout is optimized for face recognition datasets (112x96 images)
