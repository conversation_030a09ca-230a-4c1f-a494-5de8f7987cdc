#!/usr/bin/env python3
"""
简单的ArcFace-ResNet50模型实现
"""

import math
import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision.models import resnet50
from typing import Tuple, Optional, Union

class ArcMarginProduct(nn.Module):
    """ArcFace损失层
    
    参考论文：
    ArcFace: Additive Angular Margin Loss for Deep Face Recognition
    https://arxiv.org/abs/1801.07698
    
    Args:
        in_features: 输入特征维度
        out_features: 输出特征维度（类别数量）
        s: 尺度因子
        m: 角度边界
        easy_margin: 是否使用easy margin
    """
    def __init__(self, in_features: int, out_features: int, s: float = 30.0, 
                 m: float = 0.50, easy_margin: bool = False) -> None:
        super(ArcMarginProduct, self).__init__()
        self.in_features = in_features
        self.out_features = out_features
        self.s = s
        self.m = m
        self.weight = nn.Parameter(torch.FloatTensor(out_features, in_features))
        nn.init.xavier_uniform_(self.weight)

        self.easy_margin = easy_margin
        self.cos_m = math.cos(m)
        self.sin_m = math.sin(m)
        self.th = math.cos(math.pi - m)
        self.mm = math.sin(math.pi - m) * m

    def forward(self, input: torch.Tensor, label: Optional[torch.Tensor] = None) -> torch.Tensor:
        """前向传播
        
        Args:
            input: 输入特征，形状为 [batch_size, in_features]
            label: 标签，形状为 [batch_size]
            
        Returns:
            torch.Tensor: 分类结果
        """
        # 余弦相似度
        cosine = F.linear(F.normalize(input), F.normalize(self.weight))
        
        # 如果没有提供标签，直接返回余弦相似度
        if label is None:
            return cosine * self.s
            
        # 获取每个样本对应类别的相似度
        sine = torch.sqrt(1.0 - torch.pow(cosine, 2))
        
        # 计算带角度边界的余弦值 cos(θ+m)
        phi = cosine * self.cos_m - sine * self.sin_m
        
        # 对于negative cosine值使用easy margin
        if self.easy_margin:
            phi = torch.where(cosine > 0, phi, cosine)
        else:
            phi = torch.where(cosine > self.th, phi, cosine - self.mm)

        # 构造one-hot编码，以便只对目标类别应用角度边界
        one_hot = torch.zeros(cosine.size(), device=cosine.device)
        one_hot.scatter_(1, label.view(-1, 1).long(), 1)
        
        # 输出，对正确类别使用phi，对其他类别使用cosine
        output = (one_hot * phi) + ((1.0 - one_hot) * cosine)  
        output *= self.s
        
        return output

class SimpleArcFace(nn.Module):
    """使用ResNet50骨干网络的简单ArcFace模型
    
    Args:
        num_classes: 类别数量
        embedding_size: 嵌入向量维度
        s: 尺度因子
        m: 角度边界
    """
    def __init__(self, num_classes: int, embedding_size: int = 512,
                 s: float = 30.0, m: float = 0.5) -> None:
        super(SimpleArcFace, self).__init__()
        
        # 使用预训练的ResNet50删除最后的全连接层
        self.backbone = resnet50(pretrained=True)
        self.backbone_output = self.backbone.fc.in_features
        self.backbone.fc = nn.Identity()
        
        # 添加特征嵌入层
        self.embedding = nn.Sequential(
            nn.Linear(self.backbone_output, embedding_size),
            nn.BatchNorm1d(embedding_size)
        )
        
        # ArcFace损失层
        self.arcface = ArcMarginProduct(
            embedding_size,
            num_classes,
            s=s,
            m=m
        )
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self) -> None:
        """初始化模型权重"""
        for m in self.embedding.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x: torch.Tensor, labels: Optional[torch.Tensor] = None,
                return_features: bool = False) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
        """前向传播
        
        Args:
            x: 输入图像，形状为 [batch_size, 3, height, width]
            labels: 标签，形状为 [batch_size]
            return_features: 是否返回特征向量
            
        Returns:
            torch.Tensor: 分类结果或特征向量
        """
        # 提取特征
        x = self.backbone(x)
        features = self.embedding(x)
        
        # 如果只需返回特征向量
        if return_features:
            return F.normalize(features)
        
        # 计算arcface损失
        if labels is not None:
            outputs = self.arcface(features, labels)
            return outputs
        else:
            outputs = self.arcface(features)
            return outputs

def create_simple_arcface_resnet50(num_classes: int, **kwargs) -> SimpleArcFace:
    """创建SimpleArcFace-ResNet50模型
    
    Args:
        num_classes: 类别数量
        
    Returns:
        SimpleArcFace: 模型实例
    """
    model = SimpleArcFace(num_classes=num_classes, **kwargs)
    return model

if __name__ == "__main__":
    # 测试代码
    model = create_simple_arcface_resnet50(num_classes=10572)
    
    # 创建随机输入
    x = torch.randn(32, 3, 112, 112)
    labels = torch.randint(0, 10572, (32,))
    
    # 测试前向传播
    outputs = model(x, labels)
    print(f"输出形状: {outputs.shape}")
    
    # 测试特征提取
    features = model(x, return_features=True)
    print(f"特征形状: {features.shape}") 