import cv2
import numpy as np
import random
import os

def load_image(path):
    """Load image using OpenCV"""
    img = cv2.imread(path)
    if img is None:
        raise ValueError(f"Could not load image from {path}")
    return img

def low_resolution_transform(img, scale_factor=0.3):
    """Apply low resolution transformation"""
    h, w = img.shape[:2]
    # Downscale
    small = cv2.resize(img, (int(w * scale_factor), int(h * scale_factor)), interpolation=cv2.INTER_AREA)
    # Upscale back
    return cv2.resize(small, (w, h), interpolation=cv2.INTER_LINEAR)

def focus_blur_transform(img, kernel_size=15):
    """Apply focus blur (Gaussian blur)"""
    return cv2.GaussianBlur(img, (kernel_size, kernel_size), 0)

def motion_blur_transform(img, kernel_size=15, angle=45):
    """Apply motion blur"""
    # Create motion blur kernel
    kernel = np.zeros((kernel_size, kernel_size))
    kernel[int((kernel_size-1)/2), :] = np.ones(kernel_size)
    kernel = kernel / kernel_size
    
    # Rotate kernel
    center = (kernel_size // 2, kernel_size // 2)
    M = cv2.getRotationMatrix2D(center, angle, 1.0)
    kernel = cv2.warpAffine(kernel, M, (kernel_size, kernel_size))
    
    return cv2.filter2D(img, -1, kernel)

def exposure_transform(img, gamma=0.4):
    """Apply exposure adjustment (gamma correction for overexposure)"""
    # Build lookup table for gamma correction
    inv_gamma = 1.0 / gamma
    table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in np.arange(0, 256)]).astype("uint8")
    return cv2.LUT(img, table)

def backlight_transform(img, brightness_factor=0.3):
    """Apply backlight effect (darken image)"""
    return cv2.convertScaleAbs(img, alpha=brightness_factor, beta=0)

def random_occlusion_transform(img, num_patches=3, patch_size_range=(20, 40)):
    """Apply random occlusion patches"""
    result = img.copy()
    h, w = img.shape[:2]
    
    for _ in range(num_patches):
        # Random patch size
        patch_w = random.randint(*patch_size_range)
        patch_h = random.randint(*patch_size_range)
        
        # Random position
        x = random.randint(0, max(0, w - patch_w))
        y = random.randint(0, max(0, h - patch_h))
        
        # Random dark color (BGR format)
        color = [random.randint(0, 50) for _ in range(3)]
        
        # Apply patch
        result[y:y+patch_h, x:x+patch_w] = color
    
    return result

def add_centered_text(img, text, y_position, font_scale=0.7, color=(0, 0, 0), thickness=2):
    """Add centered text to image"""
    font = cv2.FONT_HERSHEY_SIMPLEX
    
    # Get text size
    text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
    
    # Calculate center position
    img_width = img.shape[1]
    text_x = (img_width - text_size[0]) // 2
    text_y = y_position
    
    # Add text
    cv2.putText(img, text, (text_x, text_y), font, font_scale, color, thickness)
    
    return img

def create_opencv_final_comparison(img1_path, img2_path, output_path="opencv_final_comparison.png"):
    """Create final OpenCV comparison exactly as requested"""
    
    # Load images
    img1 = load_image(img1_path)
    img2 = load_image(img2_path)
    
    # Define transformations
    transformations = [
        ("Low Resolution", low_resolution_transform),
        ("Focus Blur", focus_blur_transform),
        ("Motion Blur", motion_blur_transform),
        ("Exposure", exposure_transform),
        ("Backlight", backlight_transform),
        ("Random Occlusion", random_occlusion_transform)
    ]
    
    # Get image dimensions
    h, w = img1.shape[:2]
    
    # Layout parameters
    text_height = 35
    
    # Calculate canvas size
    # 2 columns: left (original) and right (transformed)
    # Each column has 2 images stacked vertically
    # NO gaps between columns as requested
    canvas_width = w * 2  # Exactly 2 image widths, no gaps
    canvas_height = (h * 2 + text_height) * len(transformations)
    
    # Create white canvas
    canvas = np.ones((canvas_height, canvas_width, 3), dtype=np.uint8) * 255
    
    for i, (transform_name, transform_func) in enumerate(transformations):
        # Calculate row position
        row_y = i * (h * 2 + text_height)
        
        # Add transformation title at the top of each group
        title_y = row_y + 25
        add_centered_text(canvas, transform_name, title_y, font_scale=0.8)
        
        # Calculate image positions
        img_start_y = row_y + text_height
        
        # Left column: Original images (Image 1 on top, Image 2 on bottom)
        canvas[img_start_y:img_start_y+h, 0:w] = img1
        canvas[img_start_y+h:img_start_y+2*h, 0:w] = img2
        
        # Right column: Transformed images (Image 1 on top, Image 2 on bottom)
        # NO gap between columns - right column starts exactly at w
        try:
            img1_transformed = transform_func(img1.copy())
            img2_transformed = transform_func(img2.copy())
            
            canvas[img_start_y:img_start_y+h, w:2*w] = img1_transformed
            canvas[img_start_y+h:img_start_y+2*h, w:2*w] = img2_transformed
            
        except Exception as e:
            print(f"Error applying {transform_name}: {str(e)}")
            # Create red error placeholder
            error_color = [0, 0, 255]  # Red in BGR
            canvas[img_start_y:img_start_y+h, w:2*w] = error_color
            canvas[img_start_y+h:img_start_y+2*h, w:2*w] = error_color
    
    # Save result
    cv2.imwrite(output_path, canvas)
    print(f"OpenCV final comparison saved to: {output_path}")
    print(f"Layout: Left column = Original images, Right column = Transformed images")
    print(f"Each group shows Image 1 (top) and Image 2 (bottom)")
    print(f"No gaps between columns as requested")
    print(f"All text labels in English")
    
    return canvas

if __name__ == "__main__":
    # Image paths
    img1_path = r"data\CASIA\CASIA-WebFace-112X96\0000268\001.jpg"
    img2_path = r"data\CASIA\CASIA-WebFace-112X96\0002743\002.jpg"
    
    # Check if images exist
    if not os.path.exists(img1_path):
        print(f"Error: Image 1 not found at {img1_path}")
        exit(1)
    
    if not os.path.exists(img2_path):
        print(f"Error: Image 2 not found at {img2_path}")
        exit(1)
    
    # Create OpenCV final comparison
    create_opencv_final_comparison(img1_path, img2_path)
