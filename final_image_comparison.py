from PIL import Image, ImageFilter, ImageEnhance, ImageDraw, ImageFont
import random
import os

def load_image(path):
    """Load image using PIL"""
    try:
        img = Image.open(path)
        return img.convert('RGB')
    except Exception as e:
        raise ValueError(f"Could not load image from {path}: {e}")

def low_resolution_transform(img, scale_factor=0.3):
    """Apply low resolution transformation"""
    w, h = img.size
    small = img.resize((int(w * scale_factor), int(h * scale_factor)), Image.LANCZOS)
    return small.resize((w, h), Image.NEAREST)

def focus_blur_transform(img, radius=5):
    """Apply focus blur (Gaussian blur)"""
    return img.filter(ImageFilter.GaussianBlur(radius=radius))

def motion_blur_transform(img):
    """Apply motion blur"""
    return img.filter(ImageFilter.BLUR)

def exposure_transform(img):
    """Apply exposure adjustment (overexposure)"""
    enhancer = ImageEnhance.Brightness(img)
    return enhancer.enhance(2.2)

def backlight_transform(img, brightness_factor=0.3):
    """Apply backlight effect (darken image)"""
    enhancer = ImageEnhance.Brightness(img)
    return enhancer.enhance(brightness_factor)

def random_occlusion_transform(img, num_patches=3, patch_size_range=(20, 40)):
    """Apply random occlusion patches"""
    result = img.copy()
    draw = ImageDraw.Draw(result)
    w, h = img.size
    
    for _ in range(num_patches):
        patch_w = random.randint(*patch_size_range)
        patch_h = random.randint(*patch_size_range)
        x = random.randint(0, max(0, w - patch_w))
        y = random.randint(0, max(0, h - patch_h))
        color = (random.randint(0, 50), random.randint(0, 50), random.randint(0, 50))
        draw.rectangle([x, y, x + patch_w, y + patch_h], fill=color)
    
    return result

def create_final_comparison(img1_path, img2_path, output_path="final_comparison.png"):
    """Create final comparison exactly as requested: left-right columns, no gaps"""
    
    # Load images
    img1 = load_image(img1_path)
    img2 = load_image(img2_path)
    
    # Define transformations
    transformations = [
        ("Low Resolution", low_resolution_transform),
        ("Focus Blur", focus_blur_transform),
        ("Motion Blur", motion_blur_transform),
        ("Exposure", exposure_transform),
        ("Backlight", backlight_transform),
        ("Random Occlusion", random_occlusion_transform)
    ]
    
    # Get image dimensions
    img_width, img_height = img1.size
    
    # Layout parameters
    text_height = 35
    
    # Calculate canvas size
    # 2 columns: left (original) and right (transformed)
    # Each column has 2 images stacked vertically (img1 on top, img2 on bottom)
    # NO gaps between columns as requested
    canvas_width = img_width * 2  # Exactly 2 image widths, no gaps
    canvas_height = (img_height * 2 + text_height) * len(transformations)
    
    # Create canvas
    canvas = Image.new('RGB', (canvas_width, canvas_height), 'white')
    draw = ImageDraw.Draw(canvas)
    
    # Try to load font
    try:
        font = ImageFont.truetype("arial.ttf", 20)
    except:
        font = ImageFont.load_default()
    
    for i, (transform_name, transform_func) in enumerate(transformations):
        # Calculate row position
        row_y = i * (img_height * 2 + text_height)
        
        # Add transformation title at the top of each group
        title_y = row_y + 5
        draw.text((canvas_width // 2, title_y), transform_name, 
                 fill='black', font=font, anchor='mt')
        
        # Calculate image positions
        img_start_y = row_y + text_height
        
        # Left column: Original images (Image 1 on top, Image 2 on bottom)
        canvas.paste(img1, (0, img_start_y))
        canvas.paste(img2, (0, img_start_y + img_height))
        
        # Right column: Transformed images (Image 1 on top, Image 2 on bottom)
        # NO gap between columns - right column starts exactly at img_width
        try:
            img1_transformed = transform_func(img1.copy())
            img2_transformed = transform_func(img2.copy())
            
            canvas.paste(img1_transformed, (img_width, img_start_y))
            canvas.paste(img2_transformed, (img_width, img_start_y + img_height))
            
        except Exception as e:
            print(f"Error applying {transform_name}: {str(e)}")
            # Create error placeholder
            error_img = Image.new('RGB', (img_width, img_height), 'red')
            canvas.paste(error_img, (img_width, img_start_y))
            canvas.paste(error_img, (img_width, img_start_y + img_height))
    
    # Save result
    canvas.save(output_path, 'PNG', quality=95)
    print(f"Final comparison saved to: {output_path}")
    print(f"Layout: Left column = Original images, Right column = Transformed images")
    print(f"Each group shows Image 1 (top) and Image 2 (bottom)")
    print(f"No gaps between columns as requested")
    return canvas

if __name__ == "__main__":
    # Image paths
    img1_path = r"data\CASIA\CASIA-WebFace-112X96\0000268\001.jpg"
    img2_path = r"data\CASIA\CASIA-WebFace-112X96\0002743\002.jpg"
    
    # Check if images exist
    if not os.path.exists(img1_path):
        print(f"Error: Image 1 not found at {img1_path}")
        exit(1)
    
    if not os.path.exists(img2_path):
        print(f"Error: Image 2 not found at {img2_path}")
        exit(1)
    
    # Create final comparison
    create_final_comparison(img1_path, img2_path)
