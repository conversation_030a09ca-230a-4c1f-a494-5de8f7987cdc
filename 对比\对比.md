# 模型对比模块实现规划

## 1. 背景与目标
- **背景**：论文中提出的 MobileFaceNet + CBAM 已在一般场景下验证效果良好。  
- **目标**：
  1. 与其它轻量级模型和主流模型在影视复杂场景（不同光照、遮挡、动态模糊等）下进行对比；
  2. 评估各模型在准确率（Recognition Accuracy）、实时性（FPS）、鲁棒性（Robustness）等维度的表现；
  3. 为论文撰写与后续部署提供数据支撑和可复现的实验模块。

## 2. 对比模型选择
为了全面评估，我们将选择以下几类模型进行对比：

## 2.1 我的模型(Baseline)
MobileFaceNet + CBAM：
骨干：MobileFaceNet
注意力机制：CBAM（卷积块注意力模块）
损失函数：ArcFace Loss (或其他训练时使用的损失函数)

## 2.2 轻量级模型(Lightweight Models)
主要选择在移动端或嵌入式设备上表现优秀，且参数量和计算量较小的模型。
MobileFaceNet (原版):作为我们模型的基线，对比CBAM模块的有效性。
SFace（简单脸）：腾讯优图提出的轻量级人脸识别模型，特点是简洁高效。

## 2.3 主流模型(Mainstream / State-of-the-Art Models)
ArcFace（ResNet-50  主干）：广泛使用的SOTA模型，提供强有力的性能参考基准。
CurricularFace：动态调整难样本的学习策略，提升模型性能。
FaceNet（Google 原版）
模型获取方式：优先考虑使用官方发布的预训练模型或社区复现的高质量预训练模型。

## 3. 数据集选择
训练集采用imfdb,测试集采用lfw



## 4. 评价指标
### 4.1 准确性(Accuracy)
TAR@FAR（真实接受率@错误接受率）：在特定误接受率下的真接受率。例如，TAR@FAR=1e-3, 1e-4, 1e-5。这是人脸验证最常用的指标。
EER（等错误率）：等错误率，即真接受率与误接受率相等时的值。

### 4.2 实时性(Real-time Performance)
推理时间(Inference Time):单张图像或批量图像在CPU/GPU上的平均推理时间（毫秒）。
帧率(FPS - Frames Per Second):每秒处理的图像帧数。
模型大小(Model Size):模型参数量（Parameters）和模型文件大小（MB）。
计算量(Computational Complexity):FLOP（浮点运算）或MAC（乘法累积运算）。

### 4.3 鲁棒性(Robustness)
鲁棒性将通过在上述“复杂场景测试子集”上的准确性表现来衡量。
专项准确率：分别计算模型在“低光照子集”、“运动模糊子集”、“遮挡子集”等各项复杂场景子集上的TAR@FAR或EER。
性能下降比例：对比模型在通用测试集和各项复杂场景子集上的性能下降幅度。

### 4.4 其他可能的补充
| 指标            | 计算方式或工具                     | 目标方向   |
| --------------- | ----------------------------------- | ---------- |
| 准确率 (Acc)    | 正确识别／总检测数                  | 越高越好   |
| 真正率 / 假正率 | ROC 曲线、AUC                       | 越高越好   |
| 实时性 (FPS)    | 实际部署测得的平均帧率              | 越高越好   |
| 模型大小        | 参数量（MB）                        | 越小越好   |
| 推理时延        | 单张图像平均推理时间（ms）          | 越短越好   |
| 鲁棒性指标      | 各场景下 Acc 差值、方差             | 差异越小越稳 |

## 5. 对比场景设计(实验设计)
我们将设计多维度的实验场景，以全面对比模型性能。
通用场景性能对比：
在数据集上进行验证（1:1验证）。
对比所有模型的TAR@FAR曲线、AUC、EER。
记录每个模型的推理时间、参数量和FLOPs。
影视复杂场景鲁棒性对比：
利用自建/筛选的“影视复杂场景测试子集”。
分别在以下子集上进行评估：
低光照子集：模拟夜戏、室内暗场。
高光/逆光子集：模拟户外强光、舞台灯光。
模糊子集：模拟快速运动、焦点不准。
遮挡子集：模拟道具、发型、面部表情（手遮脸）。
极端姿态/表情子集：模拟人物大幅度动作、夸张表演。
低分辨率子集：模拟远景、画面质量下降。
对比每个模型在不同子集上的TAR@FAR和EER。
实时性/效率对比：
在统一的硬件平台（GPU）上，测试所有模型的单张图像推理时间及批量推理时间。
生成“准确率-速度”曲线，展示模型在性能和效率之间的权衡。
量化每个模型的参数量和FLOPs。

## 6. 实施步骤
评估代码实现：
实现通用的模型推理pipeline。
实现TAR@FAR、EER、ROC曲线、AUC计算逻辑。
实现模型推理时间、参数量、FLOPs计算。

结果分析与可视化：
整理所有原始数据。
生成表格，对比各项指标。
绘制ROC曲线、准确率柱状图、速度-准确率散点图等。
对模型在不同复杂场景下的表现进行定性分析（例如，展示成功和失败案例）。
撰写报告：将实验结果、分析和结论整理成论文模块内容。

