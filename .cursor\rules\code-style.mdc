---
description: 
globs: 
alwaysApply: true
---
# 代码风格规范

## 基本原则
- 所有代码使用简体中文注释
- 遵循《代码整洁之道》原则
- 代码简洁清晰，避免不必要的复杂性

## 命名规范
- 变量和函数使用snake_case命名：`batch_size`, `learning_rate`, `calculate_loss()`
- 类名使用PascalCase：`MobileFaceNet`, `ChannelAttention`
- 常量使用大写SNAKE_CASE：`BATCH_SIZE`, `TOTAL_EPOCH`
- 私有变量和方法以下划线开头：`_make_layer()`, `_initialize_weights()`

## 函数注解
- 所有函数必须包含完整的类型注解
- 使用Google风格的文档字符串

```python
def train_model(model: nn.Module, data_loader: DataLoader, 
               optimizer: torch.optim.Optimizer) -> float:
    """训练模型一个epoch
    
    Args:
        model: 待训练的神经网络模型
        data_loader: 训练数据加载器
        optimizer: 优化器
        
    Returns:
        float: 平均训练损失
    """
```

## PyTorch特定规范
- 模型输入输出使用类型注解：`torch.Tensor`
- 使用`nn.Module`继承时，重写`forward`方法
- 模型参数初始化放在`_initialize_weights()`方法中
