---
description: 
globs: 
alwaysApply: false
---
# MobileFaceNet项目结构指南

## 项目概述
这是一个基于PyTorch的MobileFaceNet人脸识别项目，包含模型训练、评估和数据处理功能。

## 核心文件结构

### 配置文件
- [config.py](mdc:config.py): 全局配置参数，包含训练参数、模型配置、数据集路径等

### 训练脚本
- [train.py](mdc:train.py): 主训练脚本，使用CASIA数据集
- [train_imfdb.py](mdc:train_imfdb.py): IMFDB数据集训练脚本

### 评估脚本
- [lfw_eval.py](mdc:lfw_eval.py): LFW数据集评估
- [youtube_faces_eval.py](mdc:youtube_faces_eval.py): YouTube Faces数据集评估

### 核心模块 (`core/`)
- [core/model.py](mdc:core/model.py): MobileFaceNet模型定义，包含网络架构和损失函数
- [core/utils.py](mdc:core/utils.py): 工具函数

### 数据加载器 (`dataloader/`)
- [dataloader/CASIA_Face_loader.py](mdc:dataloader/CASIA_Face_loader.py): CASIA人脸数据集加载器
- [dataloader/IMFDB_loader.py](mdc:dataloader/IMFDB_loader.py): IMFDB数据集加载器
- [dataloader/LFW_loader.py](mdc:dataloader/LFW_loader.py): LFW数据集加载器

### 目录结构
```
├── config.py                 # 全局配置
├── train.py                  # 主训练脚本
├── train_imfdb.py            # IMFDB训练脚本
├── lfw_eval.py              # LFW评估
├── youtube_faces_eval.py    # YouTube Faces评估
├── core/
│   ├── model.py             # 模型定义
│   └── utils.py             # 工具函数
├── dataloader/
│   ├── CASIA_Face_loader.py # CASIA数据加载
│   ├── IMFDB_loader.py      # IMFDB数据加载
│   └── LFW_loader.py        # LFW数据加载
├── model/                   # 保存的模型检查点
├── data/                    # 数据集目录
└── result/                  # 结果输出目录
```

## 开发工作流
1. 修改配置参数请编辑 [config.py](mdc:config.py)
2. 模型架构修改请编辑 [core/model.py](mdc:core/model.py)
3. 新增数据集需在 `dataloader/` 目录创建对应加载器
4. 训练脚本统一放在根目录

