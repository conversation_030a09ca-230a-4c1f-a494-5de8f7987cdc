#!/usr/bin/env python3
"""
训练人脸识别模型 - 整合版
支持训练三种模型：
1. MobileFaceNet+CBAM (标准版)
2. MobileFaceNet基准版 (无注意力机制)
3. ArcFace-ResNet50 (简单实现)

数据集：
- 训练集/验证集：CASIA-WebFace (8:2分割)
"""

import os
import time
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.nn import DataParallel
from torch.optim.lr_scheduler import CosineAnnealingLR, LinearLR, SequentialLR
from torch.cuda.amp import GradScaler, autocast
from torch.utils.tensorboard import SummaryWriter
from torchvision import transforms
from datetime import datetime
from tqdm import tqdm
import logging
import argparse

# 导入项目配置和模块
from config import BATCH_SIZE, SAVE_FREQ, SAVE_DIR, TOTAL_EPOCH, MODEL_PRE, GPU
from config import PROCESSED_CASIA_DIR
from core import model
from core.utils import init_log
from dataloader.CASIA_Face_loader import CASIA_Face
from simple_arcface_resnet50 import create_simple_arcface_resnet50

# 允许重复dll加载
os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'

def setup_environment():
    """设置环境变量和目录"""
    # GPU初始化
    if isinstance(GPU, int):
        gpu_list = str(GPU)
        multi_gpus = False
    else:
        gpu_list = ','.join(map(str, GPU))
        multi_gpus = True
    os.environ['CUDA_VISIBLE_DEVICES'] = gpu_list
    
    # 创建必要的目录
    os.makedirs('./model', exist_ok=True)
    os.makedirs('./logs', exist_ok=True)
    
    return multi_gpus

def load_data(transform_train, transform_val, verbose=True):
    """加载训练和验证数据集
    
    Args:
        transform_train: 训练数据增强转换
        transform_val: 验证数据增强转换
        verbose: 是否显示详细加载信息
    """
    # 创建CASIA训练集和验证集 (8:2分割)
    trainset = CASIA_Face(root='', transform=transform_train, processed_dir=PROCESSED_CASIA_DIR, split='train')
    valset = CASIA_Face(root='', transform=transform_val, processed_dir=PROCESSED_CASIA_DIR, split='val')
    
    if len(trainset) == 0:
        raise RuntimeError(f"CASIA数据集 {PROCESSED_CASIA_DIR} 为空，请检查路径和结构。")
    
    # 创建数据加载器
    trainloader = torch.utils.data.DataLoader(
        trainset, 
        batch_size=BATCH_SIZE,
        shuffle=True, 
        num_workers=4,
        pin_memory=True,
        drop_last=False
    )
    
    valloader = torch.utils.data.DataLoader(
        valset, 
        batch_size=BATCH_SIZE,
        shuffle=False, 
        num_workers=4,
        pin_memory=True,
        drop_last=False
    )
    
    return trainset, trainloader, valset, valloader

def setup_optimizer_mobilefacenet(net, ArcMargin, initial_lr=0.01):
    """为MobileFaceNet设置优化器
    
    Args:
        net: MobileFaceNet模型
        ArcMargin: ArcMargin损失层
        initial_lr: 初始学习率
        
    Returns:
        tuple: (优化器, 学习率调度器)
    """
    # 分组优化参数
    ignored_params = list(map(id, net.linear1.parameters()))
    ignored_params += list(map(id, ArcMargin.weight))
    prelu_params_id = []
    prelu_params = []
    for m in net.modules():
        if isinstance(m, nn.PReLU):
            ignored_params += list(map(id, m.parameters()))
            prelu_params += m.parameters()
    base_params = filter(lambda p: id(p) not in ignored_params, net.parameters())
    
    # 优化器
    optimizer = optim.SGD([
        {'params': base_params, 'weight_decay': 4e-5},
        {'params': net.linear1.parameters(), 'weight_decay': 4e-4},
        {'params': ArcMargin.weight, 'weight_decay': 4e-4},
        {'params': prelu_params, 'weight_decay': 0.0}
    ], lr=initial_lr, momentum=0.9, nesterov=True)
    
    # 学习率预热和余弦衰减
    warmup_epochs = 5
    warmup_scheduler = LinearLR(optimizer, start_factor=0.1, end_factor=1.0, total_iters=warmup_epochs)
    cosine_scheduler = CosineAnnealingLR(optimizer, T_max=TOTAL_EPOCH-warmup_epochs, eta_min=1e-6)
    scheduler = SequentialLR(optimizer, [warmup_scheduler, cosine_scheduler], milestones=[warmup_epochs])
    
    return optimizer, scheduler

def setup_optimizer_arcface(model, initial_lr=0.01):
    """为ArcFace模型设置优化器
    
    Args:
        model: ArcFace模型
        initial_lr: 初始学习率
        
    Returns:
        tuple: (优化器, 学习率调度器)
    """
    # 优化器
    optimizer = optim.SGD(model.parameters(), lr=initial_lr, momentum=0.9, weight_decay=5e-4)
    
    # 学习率预热和余弦衰减
    warmup_epochs = 5
    warmup_scheduler = LinearLR(optimizer, start_factor=0.1, end_factor=1.0, total_iters=warmup_epochs)
    cosine_scheduler = CosineAnnealingLR(optimizer, T_max=TOTAL_EPOCH-warmup_epochs, eta_min=1e-6)
    scheduler = SequentialLR(optimizer, [warmup_scheduler, cosine_scheduler], milestones=[warmup_epochs])
    
    return optimizer, scheduler

def train_one_epoch_mobilefacenet(net, ArcMargin, trainloader, optimizer, criterion, device, scaler=None):
    """训练MobileFaceNet一个轮次"""
    net.train()
    ArcMargin.train()
    train_total_loss = 0.0
    total = 0
    correct = 0
    valid_batches = 0
    
    # 用于调试的标签和预测分布统计
    label_counts = {}
    pred_counts = {}
    
    # 训练进度条
    pbar = tqdm(trainloader, desc='训练中', leave=False, ncols=100)
    for batch_idx, (img, label) in enumerate(pbar):
        img, label = img.to(device, non_blocking=True), label.to(device, non_blocking=True)
        
        # 检查输入数据
        if torch.isnan(img).any() or torch.isinf(img).any():
            continue
            
        optimizer.zero_grad()
        
        # 使用混合精度训练
        if scaler:
            with autocast():
                raw_logits = net(img)
                if torch.isnan(raw_logits).any() or torch.isinf(raw_logits).any():
                    continue
                
                # 详细调试ArcMargin层输入
                if batch_idx == 0:
                    print(f"\n  调试 - ArcMargin输入 (Batch 0):")
                    print(f"    特征范数 (Batch 0): min={torch.norm(raw_logits, dim=1).min().item():.4f}, max={torch.norm(raw_logits, dim=1).max().item():.4f}, mean={torch.norm(raw_logits, dim=1).mean().item():.4f}")
                    # print(f"    权重范数: min={torch.norm(ArcMargin.module.weight if multi_gpus else ArcMargin.weight, dim=1).min().item():.4f}, max={torch.norm(ArcMargin.module.weight if multi_gpus else ArcMargin.weight, dim=1).max().item():.4f}, mean={torch.norm(ArcMargin.module.weight if multi_gpus else ArcMargin.weight, dim=1).mean().item():.4f}")
                    # print(f"    特征值: min={raw_logits.min().item():.4f}, max={raw_logits.max().item():.4f}, mean={raw_logits.mean().item():.4f}")
                
                output = ArcMargin(raw_logits, label)
                if torch.isnan(output).any() or torch.isinf(output).any():
                    continue
                
                # 详细调试分类器输出
                if batch_idx == 0:
                    print(f"  调试 - 分类器输出 (Batch 0):")
                    print(f"    logits (Batch 0): min={output.min().item():.4f}, max={output.max().item():.4f}, mean={output.mean().item():.4f}")
                    # print(f"    logits std: {output.std().item():.4f}")
                    
                    # 检查类别对应的logits值
                    correct_logits = output.gather(1, label.view(-1, 1))
                    print(f"    目标类logits (Batch 0): min={correct_logits.min().item():.4f}, max={correct_logits.max().item():.4f}, mean={correct_logits.mean().item():.4f}")
                
                loss = criterion(output, label)
                if torch.isnan(loss) or torch.isinf(loss):
                    continue
            
            scaler.scale(loss).backward()
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(net.parameters(), max_norm=1.0)
            torch.nn.utils.clip_grad_norm_([ArcMargin.weight], max_norm=1.0)
            scaler.step(optimizer)
            scaler.update()
        else:
            raw_logits = net(img)
            if torch.isnan(raw_logits).any() or torch.isinf(raw_logits).any():
                continue
            
            output = ArcMargin(raw_logits, label)
            if torch.isnan(output).any() or torch.isinf(output).any():
                continue
            
            loss = criterion(output, label)
            if torch.isnan(loss) or torch.isinf(loss):
                continue
            
            loss.backward()
            torch.nn.utils.clip_grad_norm_(net.parameters(), max_norm=1.0)
            torch.nn.utils.clip_grad_norm_([ArcMargin.weight], max_norm=1.0)
            optimizer.step()
        
        # 计算准确率
        with torch.no_grad():
            _, predicted = torch.max(output.data, 1)
            total += label.size(0)
            correct += (predicted == label).sum().item()
            
            # 统计标签和预测分布
            for lbl in label.cpu().numpy():
                if lbl not in label_counts:
                    label_counts[lbl] = 0
                label_counts[lbl] += 1
                
            for pred in predicted.cpu().numpy():
                if pred not in pred_counts:
                    pred_counts[pred] = 0
                pred_counts[pred] += 1
        
        train_total_loss += loss.item() * img.size(0)
        valid_batches += 1
        
        # 更新进度条信息
        pbar.set_postfix({
            'loss': f'{loss.item():.4f}',
            'acc': f'{correct/total*100:.1f}%'
        })
    
    if valid_batches == 0:
        return 0.0, 0.0
    
    avg_loss = train_total_loss / total if total > 0 else 0.0
    train_acc = 100 * correct / total if total > 0 else 0.0
    
    # 训练结束后输出分布统计
    unique_labels = len(label_counts)
    unique_preds = len(pred_counts)
    print(f"\n  训练分析: 共有 {unique_labels} 个不同标签类别, 模型预测了 {unique_preds} 个不同类别")
    
    # 计算标签和预测分布的统计信息
    if len(label_counts) > 0:
        label_freq = sorted(label_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        print(f"  前5个最常见标签: {label_freq}")
    
    if len(pred_counts) > 0:
        pred_freq = sorted(pred_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        print(f"  前5个最常见预测: {pred_freq}")
    
    if unique_preds < 10:  # 如果预测类别过少，可能存在模式崩塌
        print(f"\n  警告: 预测类别数量异常少({unique_preds}/{unique_labels})，可能存在模式崩塌问题!")
    
    return avg_loss, train_acc

def validate_one_epoch_mobilefacenet(net, ArcMargin, valloader, criterion, device):
    """验证MobileFaceNet一个轮次"""
    net.eval()
    ArcMargin.eval()
    val_total_loss = 0.0
    total = 0
    correct = 0
    
    # 用于调试的统计
    label_counts = {}
    pred_counts = {}
    
    # 验证进度条
    with torch.no_grad():
        for batch_idx, (img, label) in enumerate(valloader):
            img, label = img.to(device, non_blocking=True), label.to(device, non_blocking=True)
            
            # 前向传播
            raw_logits = net(img)
            
            # 在第一批次记录特征信息
            if batch_idx == 0:
                print(f"\n  验证调试 - 特征 (Batch 0):")
                print(f"    特征范数 (Batch 0): min={torch.norm(raw_logits, dim=1).min().item():.4f}, max={torch.norm(raw_logits, dim=1).max().item():.4f}, mean={torch.norm(raw_logits, dim=1).mean().item():.4f}")
                # print(f"    特征值: min={raw_logits.min().item():.4f}, max={raw_logits.max().item():.4f}, mean={raw_logits.mean().item():.4f}")
            
            output = ArcMargin(raw_logits, label)
            loss = criterion(output, label)
            
            # 计算准确率
            _, predicted = torch.max(output.data, 1)
            total += label.size(0)
            correct += (predicted == label).sum().item()
            
            # 统计标签和预测分布
            for lbl in label.cpu().numpy():
                if lbl not in label_counts:
                    label_counts[lbl] = 0
                label_counts[lbl] += 1
                
            for pred in predicted.cpu().numpy():
                if pred not in pred_counts:
                    pred_counts[pred] = 0
                pred_counts[pred] += 1
                
            val_total_loss += loss.item() * img.size(0)
    
    avg_loss = val_total_loss / total if total > 0 else 0.0
    val_acc = 100 * correct / total if total > 0 else 0.0
    
    # 验证完成后输出统计
    unique_labels = len(label_counts)
    unique_preds = len(pred_counts)
    print(f"\n  验证分析: 共有 {unique_labels} 个不同标签类别, 模型预测了 {unique_preds} 个不同类别")
    
    if len(pred_counts) > 0:
        pred_freq = sorted(pred_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        print(f"  前5个最常见预测: {pred_freq}")
    
    if unique_preds < 10:  # 如果预测类别过少，可能存在模式崩塌
        print(f"\n  警告: 验证预测类别数量异常少({unique_preds}/{unique_labels})，可能存在模式崩塌问题!")
    
    return avg_loss, val_acc

def train_one_epoch_arcface(model, trainloader, optimizer, criterion, device, scaler=None):
    """训练ArcFace一个轮次"""
    model.train()
    train_total_loss = 0.0
    total = 0
    correct = 0
    valid_batches = 0
    
    # 训练进度条
    pbar = tqdm(trainloader, desc='训练ArcFace', leave=False, ncols=100)
    for batch_idx, (img, label) in enumerate(pbar):
        img, label = img.to(device, non_blocking=True), label.to(device, non_blocking=True)
        
        # 检查输入数据
        if torch.isnan(img).any() or torch.isinf(img).any():
            continue
            
        optimizer.zero_grad()
        
        # 使用混合精度训练
        if scaler:
            with autocast():
                output = model(img, label)
                if torch.isnan(output).any() or torch.isinf(output).any():
                    continue
                
                loss = criterion(output, label)
                if torch.isnan(loss) or torch.isinf(loss):
                    continue
            
            scaler.scale(loss).backward()
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            scaler.step(optimizer)
            scaler.update()
        else:
            output = model(img, label)
            if torch.isnan(output).any() or torch.isinf(output).any():
                continue
            
            loss = criterion(output, label)
            if torch.isnan(loss) or torch.isinf(loss):
                continue
            
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
        
        # 计算准确率
        with torch.no_grad():
            _, predicted = torch.max(output.data, 1)
            total += label.size(0)
            correct += (predicted == label).sum().item()
        
        train_total_loss += loss.item() * img.size(0)
        valid_batches += 1
        
        # 更新进度条信息
        pbar.set_postfix({
            'loss': f'{loss.item():.4f}',
            'acc': f'{correct/total*100:.1f}%'
        })
    
    if valid_batches == 0:
        return 0.0, 0.0
    
    avg_loss = train_total_loss / total if total > 0 else 0.0
    train_acc = 100 * correct / total if total > 0 else 0.0
    
    return avg_loss, train_acc

def validate_one_epoch_arcface(model, valloader, criterion, device):
    """验证ArcFace一个轮次"""
    model.eval()
    val_total_loss = 0.0
    total = 0
    correct = 0
    
    # 验证进度条
    with torch.no_grad():
        for batch_idx, (img, label) in enumerate(valloader):
            img, label = img.to(device, non_blocking=True), label.to(device, non_blocking=True)
            
            # 前向传播
            output = model(img, label)
            loss = criterion(output, label)
            
            # 计算准确率
            _, predicted = torch.max(output.data, 1)
            total += label.size(0)
            correct += (predicted == label).sum().item()
            val_total_loss += loss.item() * img.size(0)
    
    avg_loss = val_total_loss / total if total > 0 else 0.0
    val_acc = 100 * correct / total if total > 0 else 0.0
    
    return avg_loss, val_acc

def save_checkpoint(epoch, model, save_dir, model_name, best_acc=None, optimizer=None, scheduler=None):
    """保存检查点"""
    if hasattr(model, 'module'):
        state_dict = model.module.state_dict()
    else:
        state_dict = model.state_dict()
    
    save_data = {
        'epoch': epoch, 
        'model_state_dict': state_dict
    }
    
    if best_acc:
        save_data['best_acc'] = best_acc
        save_path = os.path.join(save_dir, f'best_{model_name}.ckpt')
    else:
        save_path = os.path.join(save_dir, f'{model_name}_{epoch:03d}.ckpt')
        if optimizer:
            save_data['optimizer'] = optimizer.state_dict()
        if scheduler:
            save_data['scheduler'] = scheduler.state_dict()
    
    torch.save(save_data, save_path)

def setup_logger(save_dir, model_name):
    """设置日志为文本格式
    
    Args:
        save_dir: 保存目录
        model_name: 模型名称
        
    Returns:
        logger: 日志对象
    """
    # 确保日志目录存在
    os.makedirs(save_dir, exist_ok=True)
    
    # 创建日志文件名，使用.txt后缀
    log_file = os.path.join(save_dir, f'{model_name}_training.txt')
    
    # 配置日志
    logger = logging.getLogger(model_name)
    logger.setLevel(logging.INFO)
    
    # 清除之前的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 文件处理器
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.INFO)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 设置简洁格式，不包含日期时间
    formatter = logging.Formatter('%(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

def train_model(model_name, trainloader, valloader, device, multi_gpus, 
                class_nums, total_epochs=None, save_dir=None):
    """训练指定模型
    
    Args:
        model_name: 模型名称
        trainloader: 训练数据加载器
        valloader: 验证数据加载器
        device: 训练设备
        multi_gpus: 是否使用多GPU
        class_nums: 类别数量
        total_epochs: 训练轮数，None则使用配置值
        save_dir: 保存目录
    """
    # 如果未指定轮数，使用配置中的值
    if total_epochs is None:
        total_epochs = TOTAL_EPOCH
        
    # 如果未指定保存目录，使用默认目录
    if save_dir is None:
        save_dir = os.path.join(SAVE_DIR, f"{model_name}_casia_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    
    # 创建保存目录
    os.makedirs(save_dir, exist_ok=True)
    
    # 设置日志
    logger = setup_logger(save_dir, model_name)
    
    # 创建Tensorboard日志目录
    tb_dir = os.path.join(save_dir, 'logs')
    os.makedirs(tb_dir, exist_ok=True)
    writer = SummaryWriter(log_dir=tb_dir)
    
    logger.info(f"开始训练 {model_name}")
    logger.info(f"类别数量: {class_nums}")
    logger.info(f"保存路径: {save_dir}")
    
    # 构建模型
    if model_name == "MobileFaceNet_CBAM":
        # 带通道和空间注意力的MobileFaceNet
        net = model.MobileFacenet().to(device)
        ArcMargin = model.ArcMarginProduct(128, class_nums).to(device)
        if multi_gpus:
            net = DataParallel(net)
            ArcMargin = DataParallel(ArcMargin)
        # 优化器和学习率调度器
        optimizer, scheduler = setup_optimizer_mobilefacenet(net, ArcMargin, initial_lr=0.01)
        criterion = nn.CrossEntropyLoss()
        train_func = train_one_epoch_mobilefacenet
        val_func = validate_one_epoch_mobilefacenet
        
    elif model_name == "MobileFaceNet_Baseline":
        # 基础版MobileFaceNet，没有注意力机制
        net = model.MobileFacenetBaseline().to(device)
        ArcMargin = model.ArcMarginProduct(128, class_nums).to(device)
        if multi_gpus:
            net = DataParallel(net)
            ArcMargin = DataParallel(ArcMargin)
        # 优化器和学习率调度器
        optimizer, scheduler = setup_optimizer_mobilefacenet(net, ArcMargin, initial_lr=0.01)
        criterion = nn.CrossEntropyLoss()
        train_func = train_one_epoch_mobilefacenet
        val_func = validate_one_epoch_mobilefacenet
        
    elif model_name == "ArcFace_ResNet50":
        # 简单ArcFace ResNet50实现
        net = create_simple_arcface_resnet50(num_classes=class_nums).to(device)
        if multi_gpus:
            net = DataParallel(net)
        # 优化器和学习率调度器
        optimizer, scheduler = setup_optimizer_arcface(net, initial_lr=0.001)
        criterion = nn.CrossEntropyLoss()
        train_func = train_one_epoch_arcface
        val_func = validate_one_epoch_arcface
        ArcMargin = None
    
    # 混合精度训练
    scaler = GradScaler() if device.type == 'cuda' else None
    
    # 训练参数
    best_val_acc = 0.0
    
    logger.info(f"训练配置:")
    logger.info(f"  总轮数: {total_epochs}")
    logger.info(f"  批大小: {BATCH_SIZE}")
    logger.info(f"  混合精度: {'启用' if scaler else '禁用'}")
    
    # 主训练循环
    for epoch in range(1, total_epochs + 1):
        since = time.time()
        
        # 训练一个轮次
        if model_name.startswith('MobileFaceNet'):
            train_loss, train_acc = train_func(net, ArcMargin, trainloader, optimizer, criterion, device, scaler)
            val_loss, val_acc = val_func(net, ArcMargin, valloader, criterion, device)
        else:
            train_loss, train_acc = train_func(net, trainloader, optimizer, criterion, device, scaler)
            val_loss, val_acc = val_func(net, valloader, criterion, device)
        
        # 更新学习率
        scheduler.step()
        current_lr = scheduler.get_last_lr()[0]
        
        # 计算训练耗时
        time_elapsed = time.time() - since
        
        # 记录训练信息
        logger.info(f'轮次 {epoch}/{total_epochs}: 训练损失={train_loss:.4f}, 训练准确率={train_acc:.2f}%, 验证损失={val_loss:.4f}, 验证准确率={val_acc:.2f}%, 学习率={current_lr:.6f}, 时间={int(time_elapsed//60)}m {int(time_elapsed%60)}s')
        
        # 记录到Tensorboard
        writer.add_scalar('训练/损失', train_loss, epoch)
        writer.add_scalar('训练/准确率', train_acc, epoch)
        writer.add_scalar('验证/损失', val_loss, epoch)
        writer.add_scalar('验证/准确率', val_acc, epoch)
        writer.add_scalar('学习率', current_lr, epoch)
        
        # 根据验证集准确率保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            logger.info(f'    新的最佳验证准确率: {best_val_acc:.4f}%')
            save_checkpoint(epoch, net, save_dir, f"{model_name}_best", best_val_acc)
            
        # 定期保存检查点
        if epoch % SAVE_FREQ == 0:
            save_checkpoint(epoch, net, save_dir, model_name, optimizer=optimizer, scheduler=scheduler)
    
    writer.close()
    
    logger.info(f'{model_name} 训练完成!')
    logger.info(f'最佳验证准确率: {best_val_acc:.4f}%')
    
    return best_val_acc, net

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='训练人脸识别模型')
    parser.add_argument('--models', nargs='+', 
                       choices=['MobileFaceNet_CBAM', 'MobileFaceNet_Baseline', 'ArcFace_ResNet50', 'all'],
                       default=['all'],
                       help='要训练的模型列表，可选all表示全部')
    parser.add_argument('--epochs', type=int, default=TOTAL_EPOCH, help='训练轮数')
    parser.add_argument('--save_dir', type=str, default='./model', help='模型保存目录')
    
    args = parser.parse_args()
    
    # 处理'all'选项
    if 'all' in args.models:
        args.models = ['MobileFaceNet_CBAM', 'MobileFaceNet_Baseline', 'ArcFace_ResNet50']
    
    # 设置主日志
    main_log_dir = os.path.join(args.save_dir, 'logs_' + datetime.now().strftime('%Y%m%d_%H%M%S'))
    os.makedirs(main_log_dir, exist_ok=True)
    main_logger = setup_logger(main_log_dir, 'main')
    
    main_logger.info("开始训练人脸识别模型")
    
    # 设置环境
    multi_gpus = setup_environment()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    main_logger.info(f"设备: {device}")
    
    # 定义数据增强
    train_transform = transforms.Compose([
        transforms.RandomHorizontalFlip(),
        transforms.RandomRotation(10),  # 添加随机旋转
        transforms.ColorJitter(brightness=0.2, contrast=0.2),  # 添加颜色抖动
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
    ])
    
    val_transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
    ])
    
    # 加载数据集
    main_logger.info("加载数据集...")
    trainset, trainloader, valset, valloader = load_data(
        train_transform, val_transform
    )
    
    # 输出数据集信息
    main_logger.info(f"CASIA数据集:")
    main_logger.info(f"  训练集: {len(trainset)}样本, {trainset.class_nums}类")
    main_logger.info(f"  验证集: {len(valset)}样本")
    
    # 创建汇总日志文件
    summary_log_path = os.path.join(main_log_dir, 'training_summary.txt')
    with open(summary_log_path, 'w') as f:
        f.write(f"训练集: {len(trainset)}样本, {trainset.class_nums}类\n")
        f.write(f"验证集: {len(valset)}样本\n")
        f.write(f"计划训练模型: {', '.join(args.models)}\n\n")
    
    main_logger.info(f"\n计划训练模型: {', '.join(args.models)}")
    
    # 训练所有选定的模型
    results = {}
    trained_models = {}
    total_models = len(args.models)
    
    for i, model_name in enumerate(args.models, 1):
        main_logger.info(f"\n[{i}/{total_models}] 当前训练: {model_name}")
        
        # 为每个模型创建单独的保存目录
        model_save_dir = os.path.join(args.save_dir, f"{model_name}_casia_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        
        try:
            val_acc, trained_model = train_model(
                model_name, 
                trainloader, 
                valloader, 
                device, 
                multi_gpus, 
                trainset.class_nums, 
                args.epochs,
                model_save_dir
            )
            results[model_name] = val_acc
            trained_models[model_name] = trained_model
            
            main_logger.info(f"{model_name} 训练完成，验证准确率: {val_acc:.2f}%")
            
            # 记录到汇总日志
            with open(summary_log_path, 'a') as f:
                f.write(f"{model_name} 训练完成: 验证准确率 = {val_acc:.4f}%\n")
                f.write(f"模型保存路径: {model_save_dir}\n\n")
                
        except Exception as e:
            main_logger.error(f"{model_name} 训练失败: {e}")
            
            # 记录到汇总日志
            with open(summary_log_path, 'a') as f:
                f.write(f"{model_name} 训练失败: {str(e)}\n\n")
            
            results[model_name] = 0.0
    
    # 输出最终结果
    main_logger.info("\n" + "="*40)
    main_logger.info("训练结果汇总")
    main_logger.info("="*40)
    
    if results:
        # 按验证准确率排序
        sorted_results = sorted(results.items(), key=lambda x: x[1], reverse=True)
        
        for rank, (model_name, val_acc) in enumerate(sorted_results, 1):
            # 使用ASCII兼容符号
            status = "+" if val_acc > 0 else "-"
            main_logger.info(f"{rank}. {status} {model_name:<25} 验证准确率: {val_acc:.2f}%")
        
        # 最佳模型
        best_model_name, best_val_acc = sorted_results[0]
        if best_val_acc > 0:
            main_logger.info(f"\n最佳模型: {best_model_name}")
            main_logger.info(f"  验证准确率: {best_val_acc:.2f}%")
            
            # 记录到汇总日志
            with open(summary_log_path, 'a') as f:
                f.write(f"最佳模型: {best_model_name}, 验证准确率: {best_val_acc:.4f}%\n")
    else:
        main_logger.info("没有成功训练的模型")
        
        # 记录到汇总日志
        with open(summary_log_path, 'a') as f:
            f.write("没有成功训练的模型\n")
    
    main_logger.info("="*40)
    main_logger.info(f"训练完成！结果汇总保存在: {summary_log_path}")

if __name__ == '__main__':
    main() 