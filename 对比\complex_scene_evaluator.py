#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
复杂场景评估器模块
实现影视复杂场景下的人脸识别鲁棒性测试

"""

import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'
import cv2
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
from typing import Dict, List, Tuple, Optional
import random
from pathlib import Path
from PIL import Image, ImageFilter, ImageEnhance
import matplotlib.pyplot as plt
from tqdm import tqdm
import time
import sys

# 添加父目录到路径以便导入lfw_eval模块
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
sys.path.insert(0, str(parent_dir))

class ComplexSceneTransforms:
    """复杂场景变换类"""
    
    @staticmethod
    def simulate_low_light(image: np.ndarray, brightness_factor: float = 0.3) -> np.ndarray:
        """模拟低光照场景"""
        # 降低图像亮度
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        hsv = hsv.astype(np.float32)
        hsv[:, :, 2] = hsv[:, :, 2] * brightness_factor
        hsv = np.clip(hsv, 0, 255).astype(np.uint8)
        return cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)
    
    @staticmethod
    def simulate_high_light(image: np.ndarray, exposure_factor: float = 2.0) -> np.ndarray:
        """模拟高光/逆光场景"""
        # 增加曝光，模拟强光
        enhanced = image.astype(np.float32) * exposure_factor
        # 添加高光区域饱和
        mask = enhanced > 255
        enhanced[mask] = 255
        return enhanced.astype(np.uint8)
    
    @staticmethod
    def simulate_motion_blur(image: np.ndarray, blur_size: int = 15) -> np.ndarray:
        """模拟运动模糊"""
        # 创建运动模糊核
        kernel = np.zeros((blur_size, blur_size))
        kernel[int((blur_size-1)/2), :] = np.ones(blur_size)
        kernel = kernel / blur_size
        return cv2.filter2D(image, -1, kernel)
    
    @staticmethod
    def simulate_gaussian_blur(image: np.ndarray, kernel_size: int = 5) -> np.ndarray:
        """模拟高斯模糊"""
        return cv2.GaussianBlur(image, (kernel_size, kernel_size), 0)
    
    @staticmethod
    def simulate_occlusion(image: np.ndarray, occlusion_ratio: float = 0.3) -> np.ndarray:
        """模拟遮挡"""
        h, w = image.shape[:2]
        
        # 随机遮挡类型
        occlusion_type = random.choice(['rectangle', 'circle', 'hand'])
        
        if occlusion_type == 'rectangle':
            # 矩形遮挡(模拟墨镜、口罩等)
            x1 = random.randint(0, int(w * 0.3))
            y1 = random.randint(int(h * 0.2), int(h * 0.6))
            x2 = x1 + int(w * occlusion_ratio)
            y2 = y1 + int(h * occlusion_ratio * 0.5)
            cv2.rectangle(image, (x1, y1), (x2, y2), (0, 0, 0), -1)
            
        elif occlusion_type == 'circle':
            # 圆形遮挡
            center_x = random.randint(int(w * 0.2), int(w * 0.8))
            center_y = random.randint(int(h * 0.3), int(h * 0.7))
            radius = int(min(w, h) * occlusion_ratio * 0.5)
            cv2.circle(image, (center_x, center_y), radius, (0, 0, 0), -1)
            
        elif occlusion_type == 'hand':
            # 手部遮挡(不规则形状)
            points = []
            center_x = int(w * 0.5)
            center_y = int(h * 0.6)
            for i in range(8):
                angle = i * 45 * np.pi / 180
                radius = random.randint(int(min(w, h) * 0.1), int(min(w, h) * 0.3))
                x = center_x + int(radius * np.cos(angle))
                y = center_y + int(radius * np.sin(angle))
                points.append([x, y])
            points = np.array(points, np.int32)
            cv2.fillPoly(image, [points], (0, 0, 0))
        
        return image
    
    @staticmethod
    def simulate_random_erasing(image: np.ndarray, 
                               p: float = 1.0, 
                               scale: Tuple[float, float] = (0.02, 0.33), 
                               ratio: Tuple[float, float] = (0.3, 3.3),
                               value: int = 0) -> np.ndarray:
        """
        使用torchvision的RandomErasing方法模拟随机矩形遮挡
        
        参数:
            image: 输入图像，numpy数组格式
            p: 执行擦除的概率
            scale: 擦除区域相对于输入图像的面积比例范围
            ratio: 擦除区域的宽高比范围
            value: 填充值，默认为0（黑色）
            
        返回:
            添加随机遮挡的图像
        """
        # 将numpy图像转换为PIL图像
        pil_img = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        
        # 创建RandomErasing变换
        random_erase = transforms.RandomErasing(
            p=p, 
            scale=scale,
            ratio=ratio,
            value=value
        )
        
        # 转换为tensor
        to_tensor = transforms.ToTensor()
        img_tensor = to_tensor(pil_img)
        
        # 应用随机擦除
        erased_img_tensor = random_erase(img_tensor)
        
        # 转换回numpy格式
        to_pil = transforms.ToPILImage()
        erased_pil_img = to_pil(erased_img_tensor)
        
        # 转换回OpenCV格式
        return cv2.cvtColor(np.array(erased_pil_img), cv2.COLOR_RGB2BGR)
    
    @staticmethod
    def simulate_extreme_pose(image: np.ndarray, angle_range: Tuple[int, int] = (-30, 30)) -> np.ndarray:
        """模拟极端姿态(旋转)"""
        h, w = image.shape[:2]
        angle = random.uniform(angle_range[0], angle_range[1])
        
        # 计算旋转矩阵
        center = (w//2, h//2)
        rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
        
        # 执行旋转
        rotated = cv2.warpAffine(image, rotation_matrix, (w, h), 
                                borderMode=cv2.BORDER_REFLECT)
        return rotated
    
    @staticmethod
    def simulate_low_resolution(image: np.ndarray, scale_factor: float = 0.3) -> np.ndarray:
        """模拟低分辨率"""
        h, w = image.shape[:2]
        
        # 下采样
        small_h, small_w = int(h * scale_factor), int(w * scale_factor)
        small_image = cv2.resize(image, (small_w, small_h))
        
        # 上采样回原尺寸
        upscaled = cv2.resize(small_image, (w, h))
        return upscaled

class ComplexSceneDataset(Dataset):
    """复杂场景数据集类"""
    
    def __init__(self, image_pairs: List[Tuple[str, str]], labels: List[int], 
                 scenario: str, transform_params: Optional[Dict] = None):
        """
        初始化复杂场景数据集
        
        参数:
            image_pairs: 图像对列表 [(img1_path, img2_path), ...]
            labels: 标签列表 [0, 1, 0, 1, ...]
            scenario: 场景类型 ('low_light', 'high_light', 'blur', 'occlusion', 'extreme_pose', 'low_resolution')
            transform_params: 变换参数字典
        """
        self.image_pairs = image_pairs
        self.labels = labels
        self.scenario = scenario
        self.transform_params = transform_params or {}
        
        # 基础预处理
        self.base_transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((112, 112)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
        ])
        
        # 场景变换映射
        self.scene_transforms = {
            'low_light': ComplexSceneTransforms.simulate_low_light,
            'high_light': ComplexSceneTransforms.simulate_high_light,
            'motion_blur': ComplexSceneTransforms.simulate_motion_blur,
            'gaussian_blur': ComplexSceneTransforms.simulate_gaussian_blur,
            'occlusion': ComplexSceneTransforms.simulate_occlusion,
            'random_erasing': ComplexSceneTransforms.simulate_random_erasing,
            'extreme_pose': ComplexSceneTransforms.simulate_extreme_pose,
            'low_resolution': ComplexSceneTransforms.simulate_low_resolution
        }
    
    def __len__(self) -> int:
        return len(self.image_pairs)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor, int]:
        """获取数据项"""
        img1_path, img2_path = self.image_pairs[idx]
        label = self.labels[idx]
        
        # 加载图像
        img1 = cv2.imread(img1_path)
        img2 = cv2.imread(img2_path)
        
        if img1 is None or img2 is None:
            # 如果图像加载失败，返回零张量
            return torch.zeros(3, 112, 112), torch.zeros(3, 112, 112), label
        
        # 应用场景特定变换
        if self.scenario in self.scene_transforms:
            transform_func = self.scene_transforms[self.scenario]
            
            # 为第一张图像应用变换
            if self.scenario == 'low_light':
                brightness = self.transform_params.get('brightness_factor', 0.3)
                img1 = transform_func(img1, brightness)
            elif self.scenario == 'high_light':
                exposure = self.transform_params.get('exposure_factor', 2.0)
                img1 = transform_func(img1, exposure)
            elif self.scenario in ['motion_blur', 'gaussian_blur']:
                blur_size = self.transform_params.get('blur_size', 15)
                img1 = transform_func(img1, blur_size)
            elif self.scenario == 'occlusion':
                ratio = self.transform_params.get('occlusion_ratio', 0.3)
                img1 = transform_func(img1, ratio)
            elif self.scenario == 'random_erasing':
                p = self.transform_params.get('p', 1.0)
                scale = self.transform_params.get('scale', (0.02, 0.33))
                ratio = self.transform_params.get('ratio', (0.3, 3.3))
                value = self.transform_params.get('value', 0)
                img1 = transform_func(img1, p, scale, ratio, value)
            elif self.scenario == 'extreme_pose':
                angle_range = self.transform_params.get('angle_range', (-30, 30))
                img1 = transform_func(img1, angle_range)
            elif self.scenario == 'low_resolution':
                scale = self.transform_params.get('scale_factor', 0.3)
                img1 = transform_func(img1, scale)
            else:
                img1 = transform_func(img1)
        
        # 转换为张量
        img1_tensor = self.base_transform(img1)
        img2_tensor = self.base_transform(img2)
        
        return img1_tensor, img2_tensor, label

class ComplexSceneEvaluator:
    """复杂场景评估器"""
    
    def __init__(self, lfw_dir: str, device: str = 'cuda'):
        """
        初始化复杂场景评估器
        
        参数:
            lfw_dir: LFW数据集目录
            device: 计算设备
        """
        self.lfw_dir = lfw_dir
        self.device = device
        
        # 场景配置
        self.scenarios = {
            'low_light': {
                'name': '低光照场景',
                'description': '模拟夜戏、室内暗场',
                'params': {'brightness_factor': 0.3}
            },
            'high_light': {
                'name': '高光/逆光场景',
                'description': '模拟户外强光、舞台灯光',
                'params': {'exposure_factor': 2.0}
            },
            'motion_blur': {
                'name': '运动模糊场景',
                'description': '模拟快速运动',
                'params': {'blur_size': 15}
            },
            'gaussian_blur': {
                'name': '焦点模糊场景',
                'description': '模拟焦点不准',
                'params': {'blur_size': 5}
            },
            'occlusion': {
                'name': '遮挡场景',
                'description': '模拟道具、发型、面部表情遮挡',
                'params': {'occlusion_ratio': 0.3}
            },
            'random_erasing': {
                'name': '随机擦除遮挡场景',
                'description': '模拟随机区域遮挡',
                'params': {'p': 1.0, 'scale': (0.02, 0.33), 'ratio': (0.3, 3.3), 'value': 0}
            },
            'extreme_pose': {
                'name': '极端姿态场景',
                'description': '模拟大幅度动作、夸张表演',
                'params': {'angle_range': (-30, 30)}
            },
            'low_resolution': {
                'name': '低分辨率场景',
                'description': '模拟远景、画面质量下降',
                'params': {'scale_factor': 0.3}
            }
        }
    
    def load_lfw_pairs(self) -> Tuple[List[Tuple[str, str]], List[int]]:
        """加载LFW图像对"""
        from lfw_eval import parseList
        
        nl, nr, folds, flags = parseList(root=self.lfw_dir)
        
        # 构建图像对列表
        image_pairs = []
        for i in range(len(nl)):
            img1_path = os.path.join(self.lfw_dir, nl[i])
            img2_path = os.path.join(self.lfw_dir, nr[i])
            image_pairs.append((img1_path, img2_path))
        
        return image_pairs, flags
    
    def create_scenario_dataset(self, scenario: str, image_pairs: List[Tuple[str, str]], 
                              labels: List[int], batch_size: int = 32) -> DataLoader:
        """创建场景特定数据集"""
        if scenario not in self.scenarios:
            raise ValueError(f"不支持的场景: {scenario}")
        
        scenario_config = self.scenarios[scenario]
        dataset = ComplexSceneDataset(
            image_pairs=image_pairs,
            labels=labels,
            scenario=scenario,
            transform_params=scenario_config['params']
        )
        
        dataloader = DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=4,
            pin_memory=True
        )
        
        return dataloader
    
    def extract_features(self, model: nn.Module, dataloader: DataLoader) -> Tuple[np.ndarray, np.ndarray]:
        """提取特征向量"""
        model.to(self.device).eval()
        featureLs, featureRs = [], []
        
        with torch.no_grad():
            for img1, img2, _ in tqdm(dataloader, desc="特征提取"):
                img1 = img1.to(self.device)
                img2 = img2.to(self.device)
                
                # 提取特征
                feat1 = model(img1).cpu().numpy()
                feat2 = model(img2).cpu().numpy()
                
                featureLs.append(feat1)
                featureRs.append(feat2)
        
        featureLs = np.concatenate(featureLs, 0)
        featureRs = np.concatenate(featureRs, 0)
        return featureLs, featureRs
    
    def calculate_scenario_accuracy(self, featureLs: np.ndarray, featureRs: np.ndarray, 
                                  labels: List[int]) -> Dict[str, float]:
        """计算场景准确率"""
        # 计算余弦相似度
        scores = []
        for i in range(len(featureLs)):
            score = np.dot(featureLs[i], featureRs[i]) / (
                np.linalg.norm(featureLs[i]) * np.linalg.norm(featureRs[i])
            )
            scores.append(score)
        
        scores = np.array(scores)
        labels = np.array(labels)
        
        # 找到最佳阈值
        thresholds = np.linspace(scores.min(), scores.max(), 1000)
        best_acc = 0
        best_threshold = 0
        
        for threshold in thresholds:
            y_pred = (scores >= threshold).astype(int)
            acc = np.mean(y_pred == labels)
            if acc > best_acc:
                best_acc = acc
                best_threshold = threshold
        
        return {
            'accuracy': best_acc * 100,
            'threshold': best_threshold,
            'scores': scores,
            'labels': labels
        }
    
    def evaluate_model_robustness(self, model: nn.Module, 
                                batch_size: int = 32) -> Dict[str, float]:
        """评估模型在所有复杂场景下的鲁棒性"""
        # 加载基础数据
        image_pairs, labels = self.load_lfw_pairs()
        
        results = {}
        
        for scenario in self.scenarios.keys():
            print(f"评估场景: {self.scenarios[scenario]['name']}")
            
            # 创建场景数据集
            dataloader = self.create_scenario_dataset(scenario, image_pairs, labels, batch_size)
            
            # 提取特征
            featureLs, featureRs = self.extract_features(model, dataloader)
            
            # 计算准确率
            scenario_results = self.calculate_scenario_accuracy(featureLs, featureRs, labels)
            results[scenario] = scenario_results['accuracy']
            
            print(f"{self.scenarios[scenario]['name']}: {scenario_results['accuracy']:.2f}%")
        
        return results
    
    def visualize_scene_samples(self, scenario: str, num_samples: int = 6, 
                              save_path: Optional[str] = None) -> None:
        """可视化场景变换样本"""
        # 获取一些样本图像
        pairs, labels = self.load_lfw_pairs()
        sample_pairs = pairs[:num_samples]
        
        fig, axes = plt.subplots(num_samples, 3, figsize=(12, 4*num_samples))
        fig.suptitle(f'{self.scenarios[scenario]["name"]} 变换效果', fontsize=16)
        
        for i, (img1_path, img2_path) in enumerate(sample_pairs):
            # 加载原始图像
            img1 = cv2.imread(img1_path)
            img2 = cv2.imread(img2_path)
            
            if img1 is None or img2 is None:
                continue
                
            # RGB转换用于显示
            img1_rgb = cv2.cvtColor(img1, cv2.COLOR_BGR2RGB)
            img2_rgb = cv2.cvtColor(img2, cv2.COLOR_BGR2RGB)
            
            # 应用变换
            transform_params = self.scenarios[scenario]['params']
            
            if scenario == 'low_light':
                img1_transformed = ComplexSceneTransforms.simulate_low_light(
                    img1, transform_params.get('brightness_factor', 0.3))
            elif scenario == 'high_light':
                img1_transformed = ComplexSceneTransforms.simulate_high_light(
                    img1, transform_params.get('exposure_factor', 2.0))
            elif scenario == 'motion_blur':
                img1_transformed = ComplexSceneTransforms.simulate_motion_blur(
                    img1, transform_params.get('blur_size', 15))
            elif scenario == 'gaussian_blur':
                img1_transformed = ComplexSceneTransforms.simulate_gaussian_blur(
                    img1, transform_params.get('blur_size', 5))
            elif scenario == 'occlusion':
                img1_transformed = ComplexSceneTransforms.simulate_occlusion(
                    img1, transform_params.get('occlusion_ratio', 0.3))
            elif scenario == 'random_erasing':
                img1_transformed = ComplexSceneTransforms.simulate_random_erasing(
                    img1, transform_params.get('p', 1.0), transform_params.get('scale', (0.02, 0.33)),
                    transform_params.get('ratio', (0.3, 3.3)), transform_params.get('value', 0))
            elif scenario == 'extreme_pose':
                img1_transformed = ComplexSceneTransforms.simulate_extreme_pose(
                    img1, transform_params.get('angle_range', (-30, 30)))
            elif scenario == 'low_resolution':
                img1_transformed = ComplexSceneTransforms.simulate_low_resolution(
                    img1, transform_params.get('scale_factor', 0.3))
            else:
                img1_transformed = img1.copy()
            
            img1_transformed_rgb = cv2.cvtColor(img1_transformed, cv2.COLOR_BGR2RGB)
            
            # 显示图像
            if num_samples == 1:
                axes[0].imshow(img1_rgb)
                axes[0].set_title('原始图像1')
                axes[0].axis('off')
                
                axes[1].imshow(img1_transformed_rgb)
                axes[1].set_title(f'变换后图像1')
                axes[1].axis('off')
                
                axes[2].imshow(img2_rgb)
                axes[2].set_title('原始图像2')
                axes[2].axis('off')
            else:
                axes[i, 0].imshow(img1_rgb)
                axes[i, 0].set_title('原始图像1')
                axes[i, 0].axis('off')
                
                axes[i, 1].imshow(img1_transformed_rgb)
                axes[i, 1].set_title(f'变换后图像1')
                axes[i, 1].axis('off')
                
                axes[i, 2].imshow(img2_rgb)
                axes[i, 2].set_title('原始图像2')
                axes[i, 2].axis('off')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"   📁 场景样本已保存至: {save_path}")
        
        plt.close()
    
    def visualize_random_erasing_samples(self, num_samples: int = 6, 
                                      save_path: Optional[str] = None) -> None:
        """可视化随机擦除遮挡效果样本
        
        参数:
            num_samples: 样本数量
            save_path: 保存路径，如果为None则使用默认路径
        """
        # 设置默认保存路径
        if save_path is None:
            save_path = './comparison_results/visualizations/scene_samples_random_erasing.png'
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
        print(f"🎨 生成随机擦除遮挡效果样本...")
        
        # 获取样本图像
        pairs, labels = self.load_lfw_pairs()
        sample_pairs = pairs[:num_samples]
        
        fig, axes = plt.subplots(num_samples, 3, figsize=(15, 4*num_samples))
        fig.suptitle('随机擦除遮挡效果 (基于torchvision.transforms.RandomErasing)', fontsize=16)
        
        # 不同的随机擦除参数
        erasing_params = [
            {
                'title': '小区域遮挡',
                'p': 1.0, 
                'scale': (0.02, 0.1), 
                'ratio': (0.5, 2.0)
            },
            {
                'title': '中等区域遮挡',
                'p': 1.0, 
                'scale': (0.1, 0.25), 
                'ratio': (0.5, 2.0)
            }
        ]
        
        for i, (img1_path, img2_path) in enumerate(sample_pairs):
            # 加载原始图像
            img1 = cv2.imread(img1_path)
            
            if img1 is None:
                continue
                
            # RGB转换用于显示
            img1_rgb = cv2.cvtColor(img1, cv2.COLOR_BGR2RGB)
            
            # 应用两种不同的随机擦除效果
            erased1 = ComplexSceneTransforms.simulate_random_erasing(
                img1.copy(), 
                p=erasing_params[0]['p'],
                scale=erasing_params[0]['scale'],
                ratio=erasing_params[0]['ratio']
            )
            
            erased2 = ComplexSceneTransforms.simulate_random_erasing(
                img1.copy(), 
                p=erasing_params[1]['p'],
                scale=erasing_params[1]['scale'],
                ratio=erasing_params[1]['ratio']
            )
            
            # 显示图像
            axes[i, 0].imshow(img1_rgb)
            axes[i, 0].set_title('原始图像', fontsize=12)
            axes[i, 0].axis('off')
            
            axes[i, 1].imshow(cv2.cvtColor(erased1, cv2.COLOR_BGR2RGB))
            axes[i, 1].set_title(erasing_params[0]['title'], fontsize=12)
            axes[i, 1].axis('off')
            
            axes[i, 2].imshow(cv2.cvtColor(erased2, cv2.COLOR_BGR2RGB))
            axes[i, 2].set_title(erasing_params[1]['title'], fontsize=12)
            axes[i, 2].axis('off')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"   ✅ 随机擦除效果可视化已保存至: {save_path}")
        plt.close()
    
    def generate_robustness_report(self, model_results: Dict[str, Dict[str, float]], 
                                 save_path: str = './robustness_report.md') -> None:
        """生成鲁棒性评估报告"""
        
        report_lines = [
            "# 人脸识别模型复杂场景鲁棒性评估报告\n",
            f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n",
            "## 评估场景说明\n"
        ]
        
        # 添加场景说明
        for scenario, config in self.scenarios.items():
            report_lines.extend([
                f"### {config['name']}",
                f"- **描述**: {config['description']}",
                f"- **参数**: {config['params']}\n"
            ])
        
        # 添加结果汇总
        report_lines.extend([
            "## 评估结果汇总\n",
            "| 模型 | " + " | ".join([config['name'] for config in self.scenarios.values()]) + " |",
            "|" + "---|" * (len(self.scenarios) + 1)
        ])
        
        for model_name, results in model_results.items():
            row = f"| {model_name} |"
            for scenario in self.scenarios.keys():
                acc = results.get(scenario, 0.0)
                row += f" {acc:.2f}% |"
            report_lines.append(row)
        
        # 添加分析
        report_lines.extend([
            "\n## 性能分析\n",
            "### 各场景平均性能"
        ])
        
        # 计算场景平均性能
        scenario_avgs = {}
        for scenario in self.scenarios.keys():
            accs = [results.get(scenario, 0.0) for results in model_results.values()]
            scenario_avgs[scenario] = np.mean(accs) if accs else 0.0
        
        for scenario, avg_acc in scenario_avgs.items():
            scenario_name = self.scenarios[scenario]['name']
            report_lines.append(f"- {scenario_name}: {avg_acc:.2f}%")
        
        # 写入文件
        with open(save_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        print(f"鲁棒性评估报告已保存至: {save_path}")

def main():
    """测试主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='复杂场景鲁棒性评估')
    parser.add_argument('--lfw_dir', type=str, default='./lfw', help='LFW数据集路径')
    parser.add_argument('--scenario', type=str, default='low_light', 
                       help='测试场景')
    parser.add_argument('--visualize', action='store_true', help='可视化变换样本')
    args = parser.parse_args()
    
    evaluator = ComplexSceneEvaluator(args.lfw_dir)
    
    if args.visualize:
        # 可视化场景变换
        for scenario in evaluator.scenarios.keys():
            print(f"生成 {evaluator.scenarios[scenario]['name']} 变换样本...")
            evaluator.visualize_scene_samples(
                scenario, 
                save_path=f'./scene_samples_{scenario}.png'
            )

if __name__ == '__main__':
    main() 