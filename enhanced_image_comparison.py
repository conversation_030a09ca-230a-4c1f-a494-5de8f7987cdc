from PIL import Image, ImageFilter, ImageEnhance, ImageDraw, ImageFont
import random
import os

def load_image(path):
    """Load image using PIL"""
    try:
        img = Image.open(path)
        return img.convert('RGB')
    except Exception as e:
        raise ValueError(f"Could not load image from {path}: {e}")

def low_resolution_transform(img, scale_factor=0.3):
    """Apply low resolution transformation"""
    w, h = img.size
    small = img.resize((int(w * scale_factor), int(h * scale_factor)), Image.LANCZOS)
    return small.resize((w, h), Image.NEAREST)

def focus_blur_transform(img, radius=5):
    """Apply focus blur (Gaussian blur)"""
    return img.filter(ImageFilter.GaussianBlur(radius=radius))

def motion_blur_transform(img):
    """Apply motion blur"""
    return img.filter(ImageFilter.BLUR)

def exposure_transform(img):
    """Apply exposure adjustment (overexposure)"""
    enhancer = ImageEnhance.Brightness(img)
    return enhancer.enhance(2.2)

def backlight_transform(img, brightness_factor=0.3):
    """Apply backlight effect (darken image)"""
    enhancer = ImageEnhance.Brightness(img)
    return enhancer.enhance(brightness_factor)

def random_occlusion_transform(img, num_patches=3, patch_size_range=(20, 40)):
    """Apply random occlusion patches"""
    result = img.copy()
    draw = ImageDraw.Draw(result)
    w, h = img.size
    
    for _ in range(num_patches):
        patch_w = random.randint(*patch_size_range)
        patch_h = random.randint(*patch_size_range)
        x = random.randint(0, max(0, w - patch_w))
        y = random.randint(0, max(0, h - patch_h))
        color = (random.randint(0, 50), random.randint(0, 50), random.randint(0, 50))
        draw.rectangle([x, y, x + patch_w, y + patch_h], fill=color)
    
    return result

def create_enhanced_comparison(img1_path, img2_path, output_path="enhanced_comparison.png"):
    """Create enhanced comparison plot with better layout"""
    
    # Load images
    img1 = load_image(img1_path)
    img2 = load_image(img2_path)
    
    # Define transformations
    transformations = [
        ("Low Resolution", low_resolution_transform),
        ("Focus Blur", focus_blur_transform),
        ("Motion Blur", motion_blur_transform),
        ("Exposure", exposure_transform),
        ("Backlight", backlight_transform),
        ("Random Occlusion", random_occlusion_transform)
    ]
    
    # Get image dimensions
    img_width, img_height = img1.size
    
    # Layout parameters
    text_height = 40
    margin = 10
    
    # Calculate canvas size
    # 2 columns: original (left) and transformed (right)
    # Each column has 2 images stacked vertically (img1 on top, img2 on bottom)
    canvas_width = img_width * 2 + margin  # Small margin between columns
    canvas_height = (img_height * 2 + text_height + margin) * len(transformations)
    
    # Create canvas
    canvas = Image.new('RGB', (canvas_width, canvas_height), 'white')
    draw = ImageDraw.Draw(canvas)
    
    # Try to load font
    try:
        font = ImageFont.truetype("arial.ttf", 24)
        small_font = ImageFont.truetype("arial.ttf", 16)
    except:
        font = ImageFont.load_default()
        small_font = ImageFont.load_default()
    
    for i, (transform_name, transform_func) in enumerate(transformations):
        # Calculate row position
        row_y = i * (img_height * 2 + text_height + margin)
        
        # Add transformation title
        title_y = row_y + 5
        draw.text((canvas_width // 2, title_y), transform_name, 
                 fill='black', font=font, anchor='mt')
        
        # Calculate image positions
        img_start_y = row_y + text_height
        
        # Left column: Original images
        canvas.paste(img1, (0, img_start_y))
        canvas.paste(img2, (0, img_start_y + img_height))
        
        # Add "Original" label
        draw.text((img_width // 2, img_start_y - 20), "Original", 
                 fill='gray', font=small_font, anchor='mt')
        
        # Right column: Transformed images
        try:
            img1_transformed = transform_func(img1.copy())
            img2_transformed = transform_func(img2.copy())
            
            right_x = img_width + margin
            canvas.paste(img1_transformed, (right_x, img_start_y))
            canvas.paste(img2_transformed, (right_x, img_start_y + img_height))
            
            # Add "Transformed" label
            draw.text((right_x + img_width // 2, img_start_y - 20), "Transformed", 
                     fill='gray', font=small_font, anchor='mt')
            
        except Exception as e:
            print(f"Error applying {transform_name}: {str(e)}")
            # Create error placeholder
            error_img = Image.new('RGB', (img_width, img_height), 'red')
            right_x = img_width + margin
            canvas.paste(error_img, (right_x, img_start_y))
            canvas.paste(error_img, (right_x, img_start_y + img_height))
    
    # Save result
    canvas.save(output_path, 'PNG', quality=95)
    print(f"Enhanced comparison saved to: {output_path}")
    return canvas

if __name__ == "__main__":
    # Image paths
    img1_path = r"data\CASIA\CASIA-WebFace-112X96\0000268\001.jpg"
    img2_path = r"data\CASIA\CASIA-WebFace-112X96\0002743\002.jpg"
    
    # Check if images exist
    if not os.path.exists(img1_path):
        print(f"Error: Image 1 not found at {img1_path}")
        exit(1)
    
    if not os.path.exists(img2_path):
        print(f"Error: Image 2 not found at {img2_path}")
        exit(1)
    
    # Create enhanced comparison
    create_enhanced_comparison(img1_path, img2_path)
