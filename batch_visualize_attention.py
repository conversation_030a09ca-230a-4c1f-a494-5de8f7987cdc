#!/usr/bin/env python3
"""
批量可视化MobileFaceNet模型的空间注意力热力图

此脚本批量处理输入文件夹中的人脸图像，为每张图像生成空间注意力热力图，
并将结果保存到指定输出文件夹中。
"""

import os
import argparse
import torch
from tqdm import tqdm
from typing import List, Optional
import glob

from visualize_attention import load_model, register_hooks, preprocess_image, visualize_attention
from config import GPU


def process_images(model_path: str, 
                 input_dir: str, 
                 output_dir: str,
                 image_extensions: List[str] = ['*.jpg', '*.jpeg', '*.png']) -> None:
    """批量处理图像并生成注意力热力图
    
    Args:
        model_path: 预训练模型路径
        input_dir: 输入图像目录
        output_dir: 输出可视化结果目录
        image_extensions: 处理的图像文件扩展名列表
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置设备
    if isinstance(GPU, int):
        device = torch.device(f"cuda:{GPU}" if torch.cuda.is_available() else "cpu")
    else:
        device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    
    print(f"使用设备: {device}")
    
    # 加载模型
    model = load_model(model_path, device)
    print(f"已加载模型: {model_path}")
    
    # 注册钩子
    hooks = register_hooks(model)
    print(f"已注册 {len(hooks)} 个空间注意力钩子")
    
    # 收集所有图像路径
    image_paths = []
    for ext in image_extensions:
        image_paths.extend(glob.glob(os.path.join(input_dir, ext)))
    
    print(f"发现 {len(image_paths)} 张图像")
    
    # 处理每张图像
    for img_path in tqdm(image_paths, desc="处理图像"):
        try:
            # 获取文件名（不含扩展名）
            file_name = os.path.splitext(os.path.basename(img_path))[0]
            save_path = os.path.join(output_dir, f"{file_name}_attention.png")
            
            # 预处理图像
            img_tensor, original_image = preprocess_image(img_path, device)
            
            # 前向传播，获取注意力图
            with torch.no_grad():
                _ = model(img_tensor)
            
            # 收集所有空间注意力图
            attention_maps = {}
            for name, hook in hooks.items():
                attention_maps.update(hook.spatial_maps)
            
            # 可视化并保存结果
            visualize_attention(original_image, attention_maps, save_path)
            
        except Exception as e:
            print(f"处理图像 {img_path} 时出错: {str(e)}")


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='批量可视化MobileFaceNet空间注意力热力图')
    parser.add_argument('--model_path', type=str, default='model/best/MobileFaceNet_CBAM_best.ckpt', 
                        help='预训练模型的路径')
    parser.add_argument('--input_dir', type=str, required=True, 
                        help='输入图像目录')
    parser.add_argument('--output_dir', type=str, required=True, 
                        help='输出可视化结果目录')
    args = parser.parse_args()
    
    # 处理图像
    process_images(args.model_path, args.input_dir, args.output_dir)


if __name__ == '__main__':
    main() 