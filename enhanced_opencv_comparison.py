import cv2
import numpy as np
import random
import os

def load_image(path):
    """Load image using OpenCV"""
    img = cv2.imread(path)
    if img is None:
        raise ValueError(f"Could not load image from {path}")
    return img

def low_resolution_transform(img, scale_factor=0.25):
    """Apply low resolution transformation with better quality control"""
    h, w = img.shape[:2]
    # Downscale with area interpolation for better quality
    small = cv2.resize(img, (int(w * scale_factor), int(h * scale_factor)), interpolation=cv2.INTER_AREA)
    # Upscale back with cubic interpolation
    return cv2.resize(small, (w, h), interpolation=cv2.INTER_CUBIC)

def focus_blur_transform(img, kernel_size=21):
    """Apply focus blur with stronger effect"""
    # Use larger kernel for more noticeable blur
    return cv2.GaussianBlur(img, (kernel_size, kernel_size), 0)

def motion_blur_transform(img, kernel_size=20, angle=30):
    """Apply motion blur with improved kernel"""
    # Create motion blur kernel
    kernel = np.zeros((kernel_size, kernel_size))
    kernel[int((kernel_size-1)/2), :] = np.ones(kernel_size)
    kernel = kernel / kernel_size
    
    # Rotate kernel for motion direction
    center = (kernel_size // 2, kernel_size // 2)
    M = cv2.getRotationMatrix2D(center, angle, 1.0)
    kernel = cv2.warpAffine(kernel, M, (kernel_size, kernel_size))
    
    return cv2.filter2D(img, -1, kernel)

def exposure_transform(img, gamma=0.3):
    """Apply exposure adjustment (overexposure simulation)"""
    # More aggressive gamma correction for overexposure effect
    inv_gamma = 1.0 / gamma
    table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in np.arange(0, 256)]).astype("uint8")
    return cv2.LUT(img, table)

def backlight_transform(img, brightness_factor=0.25):
    """Apply backlight effect with contrast adjustment"""
    # Darken image and reduce contrast
    darkened = cv2.convertScaleAbs(img, alpha=brightness_factor, beta=0)
    # Add slight blue tint to simulate backlight
    darkened[:, :, 0] = np.clip(darkened[:, :, 0] * 1.1, 0, 255)  # Increase blue channel
    return darkened.astype(np.uint8)

def random_occlusion_transform(img, num_patches=4, patch_size_range=(25, 50)):
    """Apply random occlusion with various shapes"""
    result = img.copy()
    h, w = img.shape[:2]
    
    for _ in range(num_patches):
        # Random patch size
        patch_w = random.randint(*patch_size_range)
        patch_h = random.randint(*patch_size_range)
        
        # Random position
        x = random.randint(0, max(0, w - patch_w))
        y = random.randint(0, max(0, h - patch_h))
        
        # Random shape: rectangle or ellipse
        if random.choice([True, False]):
            # Rectangle
            color = [random.randint(0, 40) for _ in range(3)]
            result[y:y+patch_h, x:x+patch_w] = color
        else:
            # Ellipse
            center = (x + patch_w//2, y + patch_h//2)
            axes = (patch_w//2, patch_h//2)
            color = (random.randint(0, 40), random.randint(0, 40), random.randint(0, 40))
            cv2.ellipse(result, center, axes, 0, 0, 360, color, -1)
    
    return result

def add_text_with_background(img, text, position, font_scale=0.8, text_color=(0, 0, 0), bg_color=(255, 255, 255), thickness=2):
    """Add text with background for better visibility"""
    font = cv2.FONT_HERSHEY_SIMPLEX
    
    # Get text size
    text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
    
    # Calculate position to center text
    text_x = position[0] - text_size[0] // 2
    text_y = position[1] + text_size[1] // 2
    
    # Add background rectangle
    padding = 5
    bg_x1 = text_x - padding
    bg_y1 = text_y - text_size[1] - padding
    bg_x2 = text_x + text_size[0] + padding
    bg_y2 = text_y + padding
    
    cv2.rectangle(img, (bg_x1, bg_y1), (bg_x2, bg_y2), bg_color, -1)
    
    # Add text
    cv2.putText(img, text, (text_x, text_y), font, font_scale, text_color, thickness)
    
    return img

def create_enhanced_opencv_comparison(img1_path, img2_path, output_path="enhanced_opencv_comparison.png"):
    """Create enhanced comparison using OpenCV with better effects"""
    
    # Load images
    img1 = load_image(img1_path)
    img2 = load_image(img2_path)
    
    # Define transformations
    transformations = [
        ("Low Resolution", low_resolution_transform),
        ("Focus Blur", focus_blur_transform),
        ("Motion Blur", motion_blur_transform),
        ("Exposure", exposure_transform),
        ("Backlight", backlight_transform),
        ("Random Occlusion", random_occlusion_transform)
    ]
    
    # Get image dimensions
    h, w = img1.shape[:2]
    
    # Layout parameters
    text_height = 50
    
    # Calculate canvas size
    canvas_width = w * 2  # No gaps between columns
    canvas_height = (h * 2 + text_height) * len(transformations)
    
    # Create white canvas
    canvas = np.ones((canvas_height, canvas_width, 3), dtype=np.uint8) * 255
    
    for i, (transform_name, transform_func) in enumerate(transformations):
        # Calculate row position
        row_y = i * (h * 2 + text_height)
        
        # Add transformation title with background
        title_y = row_y + 30
        title_x = canvas_width // 2
        add_text_with_background(canvas, transform_name, (title_x, title_y), 
                               font_scale=0.9, text_color=(0, 0, 0), bg_color=(240, 240, 240))
        
        # Calculate image positions
        img_start_y = row_y + text_height
        
        # Left column: Original images
        canvas[img_start_y:img_start_y+h, 0:w] = img1
        canvas[img_start_y+h:img_start_y+2*h, 0:w] = img2
        
        # Right column: Transformed images
        try:
            img1_transformed = transform_func(img1.copy())
            img2_transformed = transform_func(img2.copy())
            
            canvas[img_start_y:img_start_y+h, w:2*w] = img1_transformed
            canvas[img_start_y+h:img_start_y+2*h, w:2*w] = img2_transformed
            
        except Exception as e:
            print(f"Error applying {transform_name}: {str(e)}")
            # Create red error placeholder
            error_color = [0, 0, 255]  # Red in BGR
            canvas[img_start_y:img_start_y+h, w:2*w] = error_color
            canvas[img_start_y+h:img_start_y+2*h, w:2*w] = error_color
    
    # Add column headers
    header_y = 20
    add_text_with_background(canvas, "Original", (w//2, header_y), 
                           font_scale=0.7, text_color=(50, 50, 50), bg_color=(220, 220, 220))
    add_text_with_background(canvas, "Transformed", (w + w//2, header_y), 
                           font_scale=0.7, text_color=(50, 50, 50), bg_color=(220, 220, 220))
    
    # Save result
    cv2.imwrite(output_path, canvas)
    print(f"Enhanced OpenCV comparison saved to: {output_path}")
    print(f"Features:")
    print(f"- Improved transformation effects")
    print(f"- Text with background for better visibility")
    print(f"- Column headers")
    print(f"- Various occlusion shapes (rectangles and ellipses)")
    
    return canvas

if __name__ == "__main__":
    # Image paths
    img1_path = r"data\CASIA\CASIA-WebFace-112X96\0000268\001.jpg"
    img2_path = r"data\CASIA\CASIA-WebFace-112X96\0002743\002.jpg"
    
    # Check if images exist
    if not os.path.exists(img1_path):
        print(f"Error: Image 1 not found at {img1_path}")
        exit(1)
    
    if not os.path.exists(img2_path):
        print(f"Error: Image 2 not found at {img2_path}")
        exit(1)
    
    # Create enhanced OpenCV comparison
    create_enhanced_opencv_comparison(img1_path, img2_path)
