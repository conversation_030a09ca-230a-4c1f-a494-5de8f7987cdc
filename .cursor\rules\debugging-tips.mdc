---
description: 
globs: 
alwaysApply: false
---
# 调试与故障排除指南

## 常见问题解决

### 1. 内存不足错误
**症状**: CUDA out of memory 或 RuntimeError
**解决方案**:
- 减小 [config.py](mdc:config.py) 中的 `BATCH_SIZE` 参数
- 检查数据加载器是否有内存泄漏
- 使用`torch.cuda.empty_cache()`清理GPU缓存

### 2. 数据加载错误
**症状**: FileNotFoundError 或 DataLoader错误
**解决方案**:
- 检查 [config.py](mdc:config.py) 中数据路径配置是否正确
- 确认数据集目录结构符合要求
- 检查对应的数据加载器 (`dataloader/*.py`) 配置

### 3. 模型加载失败
**症状**: 预训练模型无法加载
**解决方案**:
- 检查 [config.py](mdc:config.py) 中的 `RESUME` 路径
- 确认模型文件完整性
- 检查模型架构是否匹配

### 4. 训练不收敛
**症状**: 损失不下降或震荡严重
**解决方案**:
- 调整学习率（在训练脚本中修改）
- 检查数据预处理是否正确
- 验证标签格式是否正确
- 考虑使用预训练模型

## 调试工具与技巧

### 1. 打印模型结构
```python
from torchsummary import summary
model = MobileFacenet()
summary(model, (3, 112, 96))
```

### 3. 检查数据分布
在数据加载器中添加调试代码：
```python
# 在dataloader中添加
print(f"数据形状: {image.shape}")
print(f"标签范围: {label.min()} - {label.max()}")
```

### 4. 梯度检查
```python
# 检查梯度是否正常
for name, param in model.named_parameters():
    if param.grad is not None:
        print(f"{name}: {param.grad.norm()}")
```

## 性能优化建议

### 1. 数据加载优化
- 增加 `DataLoader` 的 `num_workers` 参数
- 使用 `pin_memory=True` 加速GPU传输
- 考虑使用数据预处理缓存

### 2. 训练优化
- 使用混合精度训练 (`torch.cuda.amp`)
- 启用 `torch.backends.cudnn.benchmark = True`
- 适当的批量大小平衡内存和性能

### 3. 模型优化
- 定期清理不需要的变量
- 使用 `model.eval()` 进行推理
- 考虑模型量化或剪枝

## 日志分析
- 训练损失应该逐渐下降
- 验证精度应该逐渐提升
- 注意过拟合现象（训练/验证性能差异过大）
- 监控学习率衰减情况

## 错误排查流程
1. 检查配置文件 [config.py](mdc:config.py)
2. 验证数据集路径和格式
3. 检查模型架构 [core/model.py](mdc:core/model.py)
4. 确认GPU环境配置
5. 查看完整错误堆栈信息

