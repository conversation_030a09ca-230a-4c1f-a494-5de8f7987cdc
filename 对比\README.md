# 人脸识别模型综合对比评估系统

## 📋 概述

本系统是根据《模型对比模块实现规划》文档重新设计的人脸识别模型综合对比评估系统，提供了全面的模型性能评估和复杂场景鲁棒性测试功能。

## 🎯 主要特性

### 📊 多维度评估
- **准确性评估**: TAR@FAR、EER、准确率、AUC等专业指标
- **实时性评估**: 推理时间、FPS、模型大小、参数量、FLOPs
- **鲁棒性评估**: 复杂场景下的性能表现

### 🎭 复杂场景测试
- **低光照场景**: 模拟夜戏、室内暗场
- **高光/逆光场景**: 模拟户外强光、舞台灯光  
- **模糊场景**: 模拟运动模糊、焦点不准
- **遮挡场景**: 模拟道具、发型、面部表情遮挡
- **极端姿态场景**: 模拟大幅动作、夸张表演
- **低分辨率场景**: 模拟远景、画面质量下降

### 🤖 支持模型类型
- **基准模型**: MobileFaceNet + CBAM
- **轻量级模型**: 原版MobileFaceNet、SFace
- **主流模型**: ArcFace-ResNet50、CurricularFace、FaceNet

## 📁 文件结构

```
对比/
├── comprehensive_model_comparison.py    # 主要对比器模块
├── complex_scene_evaluator.py          # 复杂场景评估器
├── run_comprehensive_comparison.py     # 统一启动脚本
├── test_system.py                      # 系统功能测试
├── demo_scene_transforms.py            # 场景变换演示
├── README.md                           # 本文档
└── 对比.md                             # 原始规划文档
```

## 🚀 快速开始

### 1. 环境准备

确保安装了以下依赖库：

```bash
pip install torch torchvision numpy opencv-python matplotlib seaborn pandas tqdm prettytable scikit-learn
```

### 2. 系统测试

运行系统功能测试：
  

```bash
python test_system.py
```

### 3. 场景变换演示

查看复杂场景变换效果：

```bash
python demo_scene_transforms.py
```

### 4. 基础对比评估

仅使用LFW数据集进行基础对比：

```bash
python run_comprehensive_comparison.py --lfw_dir /path/to/lfw
```
<!-- C:/Users/<USER>/Downloads/MobileFaceNet_Pytorch-master/data/lfw -->

### 5. 完整对比评估

包含自定义模型的完整对比：

```bash
python run_comprehensive_comparison.py \
    --lfw_dir /path/to/lfw \
    --output_dir ./results \
    --mobilefacenet_cbam_ckpt ./checkpoints/mobilefacenet_cbam.pth \
    --mobilefacenet_ckpt ./checkpoints/mobilefacenet.pth
```

### 6. 仅可视化场景变换

```bash
python run_comprehensive_comparison.py \
    --lfw_dir /path/to/lfw \
    --visualize_only
```

## 📊 输出结果

系统会生成以下文件：

### 📈 数据文件
- `comparison_results.csv` - 模型对比结果表格
- `detailed_results.json` - 详细结果数据
- `summary_report.md` - 总结报告
- `robustness_detailed_report.md` - 鲁棒性评估报告

### 📊 可视化图表
- `accuracy_comparison.png` - 准确率对比图
- `performance_comparison.png` - 性能对比图  
- `robustness_heatmap.png` - 鲁棒性热图
- `roc_curves.png` - ROC曲线对比
- `scene_samples_*.png` - 场景变换样本图像

## 🔧 高级用法

### 命令行参数

```bash
python run_comprehensive_comparison.py [选项]

必需参数:
  --lfw_dir LFW_DIR                    LFW数据集路径

可选参数:
  --output_dir OUTPUT_DIR              结果输出目录 (默认: ./comparison_results)
  --mobilefacenet_cbam_ckpt PATH       MobileFaceNet+CBAM模型检查点路径
  --mobilefacenet_ckpt PATH            原版MobileFaceNet模型检查点路径
  --skip_basic                         跳过基础模型对比
  --skip_robustness                    跳过鲁棒性评估
  --skip_visualization                 跳过场景变换可视化
  --visualize_only                     仅运行场景变换可视化
```

### 自定义评估

可以通过修改配置文件来：
- 添加新的模型类型
- 调整评估指标
- 自定义复杂场景参数
- 修改可视化样式

## 🏗️ 系统架构

### 核心模块

1. **ModelComparator** - 主要对比器
   - 模型加载和配置管理
   - 性能基准测试
   - 准确性评估
   - 结果可视化和报告生成

2. **ComplexSceneEvaluator** - 复杂场景评估器
   - 场景变换实现
   - 鲁棒性测试
   - 场景样本可视化

3. **数据类**
   - `ModelConfig` - 模型配置
   - `EvaluationMetrics` - 评估指标

### 设计原则

- **模块化**: 各功能模块独立，易于扩展
- **可配置**: 支持灵活的参数配置
- **可视化**: 丰富的图表和报告输出
- **鲁棒性**: 完善的错误处理和异常捕获

## 📝 评估指标说明

### 准确性指标
- **TAR@FAR**: 在特定误接受率下的真接受率
- **EER**: 等错误率
- **准确率**: 整体识别准确率
- **AUC**: ROC曲线下面积

### 性能指标  
- **推理时间**: 单张图像平均推理时间(ms)
- **FPS**: 每秒处理帧数
- **模型大小**: 模型文件大小(MB)
- **参数量**: 可训练参数数量(M)
- **FLOPs**: 浮点运算次数(G)

### 鲁棒性指标
- **场景准确率**: 各复杂场景下的识别准确率
- **性能下降比例**: 相对于通用场景的性能下降幅度

## 🔍 故障排除

### 常见问题

1. **导入错误**: 确保在项目根目录或对比目录中运行
2. **依赖缺失**: 运行 `python test_system.py` 检查依赖
3. **OpenMP冲突**: 设置环境变量 `KMP_DUPLICATE_LIB_OK=TRUE`
4. **中文字体警告**: 不影响功能，可忽略

### 调试建议

1. 首先运行系统测试确认环境正常
2. 使用 `--visualize_only` 选项测试基础功能
3. 检查数据集路径和格式
4. 查看详细错误日志

## 🤝 贡献指南

欢迎提交问题报告和功能请求！

### 开发环境设置

1. 克隆项目
2. 安装依赖
3. 运行测试确认环境
4. 开始开发

### 代码规范

- 使用简体中文注释
- 遵循snake_case命名规范
- 添加完整的类型注解
- 遵循《代码整洁之道》原则

## 📄 许可证

本项目遵循原项目的许可证条款。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

---

**注意**: 本系统是研究工具，用于学术研究和模型对比分析。在生产环境中使用前请进行充分测试。 