---
description: 
globs: 
alwaysApply: false
---
# MobileFaceNet模型架构指南

## 核心概念
MobileFaceNet是一个轻量级的人脸识别网络，基于MobileNet架构设计，专门针对移动设备优化。

## 主要组件

### 1. 注意力机制模块
- **ChannelAttention**: 通道注意力，关注重要的特征通道
- **SpatialAttention**: 空间注意力，关注重要的空间位置
- **CBAM**: 组合通道和空间注意力机制

### 2. 核心网络层
- **ConvBlock**: 通用卷积块，支持标准卷积和深度可分离卷积
- **Bottleneck**: 瓶颈层模块，使用倒残差结构和注意力机制
- **MobileFacenet**: 主网络架构

### 3. 损失函数
- **ArcMarginProduct**: ArcFace损失，用于人脸识别的角度边界损失

## 网络结构配置

### MobileFaceNet配置
在 [core/model.py](mdc:core/model.py) 中定义：
```python
Mobilefacenet_bottleneck_setting = [
    # [扩展因子, 输出通道数, 重复次数, 步长]
    [2, 64, 5, 2],   # 第一阶段：降采样
    [4, 128, 1, 2],  # 第二阶段：降采样
    [2, 128, 6, 1],  # 第三阶段：特征提取
    [4, 128, 1, 2],  # 第四阶段：降采样
    [2, 128, 2, 1]   # 第五阶段：特征整合
]
```

## 模型特点
1. **轻量化设计**: 使用深度可分离卷积减少参数量
2. **注意力机制**: CBAM模块增强特征表达能力
3. **残差连接**: 改善梯度流动，防止梯度消失
4. **角度损失**: ArcFace损失提高人脸识别精度

## 输入输出规格
- **输入**: `torch.Tensor`，形状为 `(batch_size, 3, 112, 96)`
- **输出**: `torch.Tensor`，128维特征向量
- **损失输出**: ArcMarginProduct层输出分类logits

## 自定义模型
如需修改网络结构，请：
1. 编辑 [core/model.py](mdc:core/model.py) 中的配置参数
2. 调整 `Mobilefacenet_bottleneck_setting` 列表
3. 修改对应的通道数和层数配置
4. 确保 `_initialize_weights()` 方法正确初始化新加层

